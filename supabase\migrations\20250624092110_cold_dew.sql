/*
  # Seed sample data for development

  This migration adds sample data for testing and development purposes.
  It should only be run in development environments.

  1. Sample study progress data
  2. Sample quiz results
  3. Sample study sessions
*/

-- Note: This is sample data for development only
-- In production, this should be skipped or removed

-- Insert sample study progress (only if no data exists)
DO $$
BEGIN
  -- Only insert if profiles table has data and study_progress is empty
  IF EXISTS (SELECT 1 FROM profiles LIMIT 1) AND NOT EXISTS (SELECT 1 FROM study_progress LIMIT 1) THEN
    
    -- Get the first user ID for sample data
    INSERT INTO study_progress (user_id, subject, chapter, progress, completed)
    SELECT 
      p.id,
      unnest(ARRAY['Physics', 'Chemistry', 'Mathematics', 'Biology', 'English']) as subject,
      'Sample Chapter' as chapter,
      (random() * 100)::integer as progress,
      (random() > 0.7) as completed
    FROM profiles p
    LIMIT 1;

    -- Insert sample quiz results
    INSERT INTO quiz_results (user_id, subject, quiz_title, score, total_questions)
    SELECT 
      p.id,
      unnest(ARRAY['Physics', 'Chemistry', 'Mathematics']) as subject,
      'Sample Quiz' as quiz_title,
      (random() * 10 + 5)::integer as score,
      15 as total_questions
    FROM profiles p
    LIMIT 1;

    -- Insert sample study sessions
    INSERT INTO study_sessions (user_id, subject, duration, created_at)
    SELECT 
      p.id,
      unnest(ARRAY['Physics', 'Chemistry', 'Mathematics', 'Biology']) as subject,
      (random() * 120 + 30)::integer as duration,
      now() - (random() * interval '30 days') as created_at
    FROM profiles p
    LIMIT 1;

  END IF;
END $$;