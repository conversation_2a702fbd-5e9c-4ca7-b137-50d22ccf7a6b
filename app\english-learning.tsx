import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Play, Volume2, BookOpen, MessageCircle, PenTool, Mic } from 'lucide-react-native';
import { router } from 'expo-router';

const englishLearningModules = [
  {
    id: 'basics',
    title: 'English Basics',
    subtitle: 'Start from zero - Learn alphabet, numbers, basic words',
    icon: '🔤',
    color: '#EF4444',
    lessons: [
      'English Alphabet & Pronunciation',
      'Numbers 1-100',
      'Basic Greetings (Hello, Good Morning)',
      'Family Members (Mother, Father, Sister)',
      'Body Parts (Head, Hand, Eye)',
      'Colors (Red, Blue, Green)',
      'Days of Week',
      'Months of Year'
    ]
  },
  {
    id: 'vocabulary',
    title: 'Daily Vocabulary',
    subtitle: 'Learn 1000 most common English words with Marathi meaning',
    icon: '📝',
    color: '#F59E0B',
    lessons: [
      'Home & Family (100 words)',
      'Food & Drinks (100 words)',
      'School & Education (100 words)',
      'Animals & Nature (100 words)',
      'Transportation (100 words)',
      'Emotions & Feelings (100 words)',
      'Time & Weather (100 words)',
      'Shopping & Money (100 words)',
      'Health & Body (100 words)',
      'Technology & Modern Life (100 words)'
    ]
  },
  {
    id: 'grammar',
    title: 'Grammar Foundation',
    subtitle: 'Learn basic grammar rules step by step',
    icon: '📚',
    color: '#10B981',
    lessons: [
      'What is a Sentence?',
      'Subject and Predicate',
      'Nouns (Person, Place, Thing)',
      'Pronouns (I, You, He, She)',
      'Verbs (Action Words)',
      'Adjectives (Describing Words)',
      'Articles (A, An, The)',
      'Simple Present Tense',
      'Simple Past Tense',
      'Simple Future Tense'
    ]
  },
  {
    id: 'conversation',
    title: 'Speaking Practice',
    subtitle: 'Practice daily conversations in English',
    icon: '🗣️',
    color: '#3B82F6',
    lessons: [
      'Self Introduction',
      'Asking for Help',
      'Shopping Conversations',
      'At the Restaurant',
      'Asking Directions',
      'Phone Conversations',
      'Job Interview Basics',
      'Talking about Hobbies',
      'Weather Conversations',
      'Making Plans with Friends'
    ]
  },
  {
    id: 'reading',
    title: 'Reading Skills',
    subtitle: 'Start with simple stories, build to HSC level',
    icon: '📖',
    color: '#8B5CF6',
    lessons: [
      'Simple 2-3 Line Stories',
      'Short Paragraphs',
      'News Headlines',
      'Simple Articles',
      'HSC Prose Passages',
      'Poetry Understanding',
      'Comprehension Practice',
      'Speed Reading',
      'Critical Reading',
      'Literature Analysis'
    ]
  },
  {
    id: 'writing',
    title: 'Writing Skills',
    subtitle: 'From simple sentences to essays and letters',
    icon: '✍️',
    color: '#06B6D4',
    lessons: [
      'Writing Simple Sentences',
      'Paragraph Writing',
      'Letter Writing (Formal/Informal)',
      'Essay Writing Basics',
      'Story Writing',
      'Report Writing',
      'Email Writing',
      'Application Writing',
      'HSC Writing Formats',
      'Creative Writing'
    ]
  }
];

export default function EnglishLearningScreen() {
  const [selectedModule, setSelectedModule] = useState<any>(null);

  const handleModulePress = (module: any) => {
    setSelectedModule(module);
  };

  const startLesson = (lesson: string) => {
    Alert.alert(
      'Start Lesson',
      `Starting: ${lesson}\n\nThis will include:\n• Audio pronunciation\n• Marathi translation\n• Practice exercises\n• Speaking practice`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Start Learning', onPress: () => {
          // Here you would navigate to the actual lesson
          Alert.alert('Coming Soon', 'Lesson content will be available soon!');
        }}
      ]
    );
  };

  if (selectedModule) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={[selectedModule.color, selectedModule.color + 'CC']}
          style={styles.header}
        >
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => setSelectedModule(null)}
          >
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.moduleIcon}>{selectedModule.icon}</Text>
            <Text style={styles.moduleTitle}>{selectedModule.title}</Text>
            <Text style={styles.moduleSubtitle}>{selectedModule.subtitle}</Text>
          </View>
        </LinearGradient>

        <ScrollView style={styles.lessonsContainer}>
          <Text style={styles.lessonsTitle}>Lessons ({selectedModule.lessons.length})</Text>
          {selectedModule.lessons.map((lesson: string, index: number) => (
            <TouchableOpacity
              key={index}
              style={styles.lessonCard}
              onPress={() => startLesson(lesson)}
            >
              <View style={styles.lessonNumber}>
                <Text style={styles.lessonNumberText}>{index + 1}</Text>
              </View>
              <View style={styles.lessonContent}>
                <Text style={styles.lessonTitle}>{lesson}</Text>
                <Text style={styles.lessonDuration}>~15 minutes</Text>
              </View>
              <Play size={20} color={selectedModule.color} />
            </TouchableOpacity>
          ))}
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#EF4444', '#DC2626']}
        style={styles.header}
      >
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.title}>English Learning</Text>
          <Text style={styles.subtitle}>मराठी ते इंग्रजी - Step by Step</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.modulesContainer}>
        <View style={styles.motivationCard}>
          <Text style={styles.motivationTitle}>🎯 Your English Journey</Text>
          <Text style={styles.motivationText}>
            Start from basics and build confidence. Each lesson includes Marathi explanations to help you understand better.
          </Text>
        </View>

        {englishLearningModules.map((module) => (
          <TouchableOpacity
            key={module.id}
            style={styles.moduleCard}
            onPress={() => handleModulePress(module)}
          >
            <LinearGradient
              colors={[module.color + '15', module.color + '08']}
              style={styles.moduleGradient}
            >
              <View style={styles.moduleHeader}>
                <View style={[styles.moduleIconContainer, { backgroundColor: module.color + '20' }]}>
                  <Text style={styles.moduleIconText}>{module.icon}</Text>
                </View>
                <View style={styles.moduleInfo}>
                  <Text style={styles.moduleCardTitle}>{module.title}</Text>
                  <Text style={styles.moduleCardSubtitle}>{module.subtitle}</Text>
                </View>
              </View>
              <View style={styles.moduleStats}>
                <Text style={[styles.lessonsCount, { color: module.color }]}>
                  {module.lessons.length} Lessons
                </Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        ))}

        <View style={styles.tipsCard}>
          <Text style={styles.tipsTitle}>💡 Learning Tips</Text>
          <Text style={styles.tipsText}>
            • Practice 15-20 minutes daily{'\n'}
            • Don't worry about mistakes{'\n'}
            • Use AI tutor for doubts{'\n'}
            • Practice speaking aloud{'\n'}
            • Review previous lessons regularly
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 24,
    paddingHorizontal: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 4,
  },
  moduleIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  moduleTitle: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  moduleSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 4,
  },
  modulesContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  motivationCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  motivationTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  motivationText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 22,
  },
  moduleCard: {
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  moduleGradient: {
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  moduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  moduleIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  moduleIconText: {
    fontSize: 28,
  },
  moduleInfo: {
    flex: 1,
  },
  moduleCardTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
  },
  moduleCardSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  moduleStats: {
    alignItems: 'flex-end',
  },
  lessonsCount: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  lessonsContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  lessonsTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  lessonCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  lessonNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  lessonNumberText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  lessonContent: {
    flex: 1,
  },
  lessonTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  lessonDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginTop: 2,
  },
  tipsCard: {
    backgroundColor: '#F0FDF4',
    borderRadius: 16,
    padding: 20,
    marginTop: 16,
    marginBottom: 32,
  },
  tipsTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#059669',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#047857',
    lineHeight: 20,
  },
});
