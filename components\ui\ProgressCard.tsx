import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withDelay } from 'react-native-reanimated';

interface ProgressCardProps {
  title: string;
  progress: number;
  total: number;
  color: string;
  icon: React.ReactNode;
  delay?: number;
}

export default function ProgressCard({ title, progress, total, color, icon, delay = 0 }: ProgressCardProps) {
  const progressWidth = useSharedValue(0);
  const cardOpacity = useSharedValue(0);
  const cardTranslateY = useSharedValue(20);

  React.useEffect(() => {
    cardOpacity.value = withDelay(delay, withTiming(1, { duration: 600 }));
    cardTranslateY.value = withDelay(delay, withTiming(0, { duration: 600 }));
    progressWidth.value = withDelay(delay + 300, withTiming((progress / total) * 100, { duration: 800 }));
  }, [progress, total, delay]);

  const animatedCardStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ translateY: cardTranslateY.value }],
  }));

  const animatedProgressStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const percentage = Math.round((progress / total) * 100);

  return (
    <Animated.View style={[styles.container, animatedCardStyle]}>
      <LinearGradient
        colors={[color + '08', color + '03']}
        style={styles.gradient}
      >
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
            {icon}
          </View>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.subtitle}>{progress} of {total} completed</Text>
          </View>
          <View style={styles.percentageContainer}>
            <Text style={[styles.percentage, { color }]}>{percentage}%</Text>
            {percentage >= 80 && (
              <View style={styles.achievementBadge}>
                <Text style={styles.achievementText}>🏆</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressTrack}>
            <Animated.View
              style={[
                styles.progressBar,
                { backgroundColor: color },
                animatedProgressStyle
              ]}
            />
          </View>
          <View style={styles.progressLabels}>
            <Text style={styles.progressLabel}>Progress</Text>
            <Text style={[styles.progressStatus, { color: percentage >= 70 ? '#10B981' : percentage >= 40 ? '#F59E0B' : '#EF4444' }]}>
              {percentage >= 70 ? 'Excellent' : percentage >= 40 ? 'Good' : 'Keep Going'}
            </Text>
          </View>
        </View>

        {/* Floating Action Indicator */}
        <View style={[styles.actionIndicator, { backgroundColor: color + '20' }]}>
          <Text style={[styles.actionText, { color }]}>Tap to continue</Text>
        </View>
      </LinearGradient>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 6,
    backgroundColor: '#FFFFFF',
  },
  gradient: {
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 52,
    height: 52,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  percentageContainer: {
    alignItems: 'center',
    position: 'relative',
  },
  percentage: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
  },
  achievementBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FEF3C7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementText: {
    fontSize: 12,
  },
  progressContainer: {
    marginTop: 12,
  },
  progressTrack: {
    height: 10,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 12,
  },
  progressBar: {
    height: '100%',
    borderRadius: 6,
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#9CA3AF',
  },
  progressStatus: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  actionIndicator: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  actionText: {
    fontSize: 11,
    fontFamily: 'Inter-SemiBold',
  },
});
