import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from './useAuth';

export interface StudyProgress {
  id: string;
  subject: string;
  chapter: string;
  progress: number;
  completed: boolean;
  created_at: string;
  updated_at: string;
}

export function useStudyProgress() {
  const { user } = useAuth();
  const [progress, setProgress] = useState<StudyProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchProgress();
    }
  }, [user]);

  const fetchProgress = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('study_progress')
        .select('*')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false });

      if (error) throw error;
      setProgress(data || []);
    } catch (error) {
      console.error('Error fetching progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateProgress = async (subject: string, chapter: string, progressValue: number, completed: boolean = false) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('study_progress')
        .upsert({
          user_id: user.id,
          subject,
          chapter,
          progress: progressValue,
          completed,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id,subject,chapter'
        });

      if (error) throw error;
      await fetchProgress();
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  return {
    progress,
    loading,
    updateProgress,
    refetch: fetchProgress,
  };
}