import { useState, useEffect } from 'react';
import { storage, UserProgress } from '@/lib/storage';

export function useStudyProgress() {
  const [progress, setProgress] = useState<UserProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProgress();
  }, []);

  const fetchProgress = async () => {
    try {
      setLoading(true);
      const data = await storage.getUserProgress();
      setProgress(data);
    } catch (error) {
      console.error('Error fetching progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateProgress = async (subject: string, topic: string, completed: boolean, score?: number, timeSpent: number = 0) => {
    try {
      await storage.updateTopicProgress(subject, topic, completed, score, timeSpent);

      // Update local state
      setProgress(prev => {
        const existingIndex = prev.findIndex(p => p.subject === subject && p.topic === topic);
        const updatedProgress: UserProgress = {
          subject,
          topic,
          completed,
          score,
          timeSpent,
          lastStudied: new Date().toISOString(),
        };

        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = updatedProgress;
          return updated;
        } else {
          return [...prev, updatedProgress];
        }
      });
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const getSubjectProgress = (subjectId: string) => {
    const subjectProgress = progress.filter(p => p.subject.toLowerCase() === subjectId.toLowerCase());
    const completedTopics = subjectProgress.filter(p => p.completed).length;
    const totalProgress = subjectProgress.length > 0 ? Math.round((completedTopics / subjectProgress.length) * 100) : 0;

    return {
      completedTopics,
      totalProgress,
      totalTimeSpent: subjectProgress.reduce((sum, p) => sum + p.timeSpent, 0),
    };
  };

  return {
    progress,
    loading,
    updateProgress,
    getSubjectProgress,
    refetch: fetchProgress,
  };
}