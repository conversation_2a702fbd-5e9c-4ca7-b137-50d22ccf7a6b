import { Alert } from 'react-native';

// API key should be set in .env file as EXPO_PUBLIC_GEMINI_API_KEY

export interface EnglishLessonContent {
  title: string;
  description: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: number; // in minutes
  sections: LessonSection[];
}

export interface LessonSection {
  type: 'introduction' | 'vocabulary' | 'grammar' | 'practice' | 'conversation' | 'exercise';
  title: string;
  content: any;
}

export interface VocabularyItem {
  english: string;
  marathi: string;
  pronunciation: string;
  example: string;
  marathiExample: string;
}

export interface GrammarRule {
  rule: string;
  explanation: string;
  marathiExplanation: string;
  examples: Array<{
    english: string;
    marathi: string;
    correct: boolean;
  }>;
}

export interface ConversationPractice {
  scenario: string;
  marathiScenario: string;
  dialogue: Array<{
    speaker: 'A' | 'B';
    english: string;
    marathi: string;
    pronunciation: string;
  }>;
}

export interface Exercise {
  type: 'multiple_choice' | 'fill_blank' | 'translation' | 'pronunciation';
  question: string;
  marathiQuestion?: string;
  options?: string[];
  correctAnswer: string | number;
  explanation: string;
  marathiExplanation: string;
}

export class AIEnglishTutor {
  private static async callGeminiAPI(prompt: string): Promise<string> {
    try {
      const API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
      if (!API_KEY || API_KEY === 'YOUR_GEMINI_API_KEY_HERE' || API_KEY.trim() === '') {
        throw new Error('API_KEY_MISSING');
      }

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 3000,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Invalid response format from Gemini API');
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      throw error;
    }
  }

  static async generatePersonalizedLesson(
    topic: string,
    studentLevel: 'beginner' | 'intermediate' | 'advanced',
    weakAreas: string[] = [],
    previousMistakes: string[] = []
  ): Promise<EnglishLessonContent> {
    const levelDescription = {
      'beginner': 'The student is just starting to learn English. Use very simple words and provide extensive Marathi explanations.',
      'intermediate': 'The student has basic English knowledge but needs to improve fluency and confidence.',
      'advanced': 'The student has good English skills but wants to perfect grammar and expand vocabulary.'
    };

    const weakAreasContext = weakAreas.length > 0
      ? `Focus extra attention on these weak areas: ${weakAreas.join(', ')}.`
      : '';

    const mistakesContext = previousMistakes.length > 0
      ? `The student commonly makes these mistakes: ${previousMistakes.join(', ')}. Address these specifically.`
      : '';

    const prompt = `
Create a comprehensive English lesson for a Marathi-speaking HSC Class 12 student.

Student Profile:
- Level: ${studentLevel}
- ${levelDescription[studentLevel]}
- Native Language: Marathi
- ${weakAreasContext}
- ${mistakesContext}

Lesson Topic: ${topic}

Requirements:
1. Create a structured lesson with multiple sections
2. Include Marathi translations for all English content
3. Provide pronunciation guides using simple phonetics
4. Include practical examples relevant to a 17-18 year old student
5. Add interactive exercises to test understanding
6. Make it engaging and confidence-building

Format your response as a valid JSON object with this structure:
{
  "title": "Lesson title",
  "description": "Brief description of what student will learn",
  "difficulty": "${studentLevel}",
  "estimatedTime": 20,
  "sections": [
    {
      "type": "introduction",
      "title": "Introduction",
      "content": {
        "text": "Introduction text in English",
        "marathiText": "Introduction text in Marathi",
        "objectives": ["Objective 1", "Objective 2"]
      }
    },
    {
      "type": "vocabulary",
      "title": "Key Vocabulary",
      "content": {
        "words": [
          {
            "english": "word",
            "marathi": "मराठी शब्द",
            "pronunciation": "pronunciation guide",
            "example": "Example sentence in English",
            "marathiExample": "Example sentence in Marathi"
          }
        ]
      }
    },
    {
      "type": "grammar",
      "title": "Grammar Focus",
      "content": {
        "rules": [
          {
            "rule": "Grammar rule in English",
            "explanation": "Detailed explanation in English",
            "marathiExplanation": "Detailed explanation in Marathi",
            "examples": [
              {
                "english": "Correct example",
                "marathi": "Marathi translation",
                "correct": true
              }
            ]
          }
        ]
      }
    },
    {
      "type": "conversation",
      "title": "Conversation Practice",
      "content": {
        "scenario": "Conversation scenario in English",
        "marathiScenario": "Conversation scenario in Marathi",
        "dialogue": [
          {
            "speaker": "A",
            "english": "English dialogue",
            "marathi": "Marathi translation",
            "pronunciation": "pronunciation guide"
          }
        ]
      }
    },
    {
      "type": "exercise",
      "title": "Practice Exercises",
      "content": {
        "exercises": [
          {
            "type": "multiple_choice",
            "question": "Question in English",
            "marathiQuestion": "Question in Marathi",
            "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
            "correctAnswer": 0,
            "explanation": "Explanation in English",
            "marathiExplanation": "Explanation in Marathi"
          }
        ]
      }
    }
  ]
}

Generate the lesson now:`;

    try {
      const response = await this.callGeminiAPI(prompt);

      // Clean the response and extract JSON
      let cleanResponse = response.trim();

      // Remove markdown code blocks if present
      cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Extract JSON from response
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error('No JSON object found in response:', cleanResponse);
        throw new Error('No valid JSON found in response');
      }

      let lessonData;
      try {
        lessonData = JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        console.error('Attempted to parse:', jsonMatch[0]);
        throw new Error('Invalid JSON format in response');
      }

      // Validate the lesson data structure
      if (!lessonData.title || !lessonData.sections || !Array.isArray(lessonData.sections)) {
        console.error('Invalid lesson data structure:', lessonData);
        throw new Error('Invalid lesson data structure');
      }

      return lessonData as EnglishLessonContent;
    } catch (error) {
      console.error('Error generating lesson:', error);

      // Always return fallback lesson
      return this.getFallbackLesson(topic, studentLevel);
    }
  }

  private static getFallbackLesson(topic: string, level: string): EnglishLessonContent {
    return {
      title: `${topic} - English Basics`,
      description: 'Learn English fundamentals with Marathi support',
      difficulty: level as 'Beginner' | 'Intermediate' | 'Advanced',
      estimatedTime: 15,
      sections: [
        {
          type: 'introduction',
          title: 'Introduction',
          content: {
            text: `Welcome to learning ${topic} in English!`,
            marathiText: `${topic} इंग्रजीत शिकण्यासाठी स्वागत!`,
            objectives: ['Learn basic vocabulary', 'Practice pronunciation', 'Build confidence']
          }
        },
        {
          type: 'vocabulary',
          title: 'Key Words',
          content: {
            words: [
              {
                english: 'Hello',
                marathi: 'नमस्कार',
                pronunciation: 'heh-loh',
                example: 'Hello, how are you?',
                marathiExample: 'नमस्कार, तुम्ही कसे आहात?'
              }
            ]
          }
        }
      ]
    };
  }

  static async generateConversationPractice(
    scenario: string,
    difficulty: 'beginner' | 'intermediate' | 'advanced'
  ): Promise<ConversationPractice> {
    const prompt = `
Create a conversation practice for a Marathi-speaking student learning English.

Scenario: ${scenario}
Difficulty: ${difficulty}

Create a realistic dialogue between two people (A and B) with:
1. 6-8 exchanges
2. Natural, age-appropriate language for HSC students
3. Marathi translations for each line
4. Simple pronunciation guides
5. Practical phrases they can use in real life

Format as JSON:
{
  "scenario": "Scenario description in English",
  "marathiScenario": "Scenario description in Marathi",
  "dialogue": [
    {
      "speaker": "A",
      "english": "English dialogue",
      "marathi": "Marathi translation",
      "pronunciation": "pronunciation guide"
    }
  ]
}`;

    try {
      const response = await this.callGeminiAPI(prompt);
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Error generating conversation:', error);
      return {
        scenario: 'Basic greeting conversation',
        marathiScenario: 'मूलभूत अभिवादन संभाषण',
        dialogue: [
          {
            speaker: 'A',
            english: 'Hello! How are you?',
            marathi: 'नमस्कार! तुम्ही कसे आहात?',
            pronunciation: 'heh-loh! how aar yoo?'
          },
          {
            speaker: 'B',
            english: 'I am fine, thank you!',
            marathi: 'मी ठीक आहे, धन्यवाद!',
            pronunciation: 'ahy am fahyn, thank yoo!'
          }
        ]
      };
    }
  }

  static async generateVocabularyList(
    category: string,
    count: number = 10,
    difficulty: 'beginner' | 'intermediate' | 'advanced'
  ): Promise<VocabularyItem[]> {
    const prompt = `
Generate ${count} English vocabulary words for the category "${category}" suitable for a ${difficulty} level Marathi-speaking HSC student.

For each word provide:
1. English word
2. Marathi translation
3. Simple pronunciation guide
4. Example sentence in English
5. Example sentence in Marathi

Format as JSON array:
[
  {
    "english": "word",
    "marathi": "मराठी शब्द",
    "pronunciation": "pronunciation",
    "example": "English example sentence",
    "marathiExample": "मराठी उदाहरण वाक्य"
  }
]`;

    try {
      const response = await this.callGeminiAPI(prompt);
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Error generating vocabulary:', error);
      return [
        {
          english: 'book',
          marathi: 'पुस्तक',
          pronunciation: 'buk',
          example: 'I read a book every day.',
          marathiExample: 'मी दररोज एक पुस्तक वाचतो.'
        }
      ];
    }
  }
}
