import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Search, Play, BookOpen, Clock, CircleCheck as CheckCircle, Youtube } from 'lucide-react-native';

export default function SubjectDetailScreen() {
  const { id, name, color } = useLocalSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('chapters');

  const subjectData = {
    physics: {
      chapters: [
        { id: '1', title: 'Rotational Dynamics', completed: true, duration: '2h 30m', videos: 12, description: 'Learn about angular motion, torque, and moment of inertia' },
        { id: '2', title: 'Mechanical Properties of Fluids', completed: true, duration: '1h 45m', videos: 8, description: 'Study fluid statics, dynamics, and surface tension' },
        { id: '3', title: 'Kinetic Theory of Gases & Radiation', completed: false, duration: '2h 15m', videos: 10, description: 'Understand molecular motion and thermal radiation' },
        { id: '4', title: 'Electrostatics', completed: false, duration: '3h 00m', videos: 15, description: 'Electric charges, fields, and potentials' },
        { id: '5', title: 'Current Electricity', completed: false, duration: '2h 45m', videos: 13, description: 'Electric current, resistance, and circuits' },
        { id: '6', title: 'Magnetic Effects of Current', completed: false, duration: '2h 30m', videos: 11, description: 'Magnetism due to electric current' },
        { id: '7', title: 'Electromagnetic Induction', completed: false, duration: '2h 20m', videos: 9, description: 'Faradays law and electromagnetic induction' },
        { id: '8', title: 'Electromagnetic Waves', completed: false, duration: '1h 30m', videos: 7, description: 'Properties and propagation of EM waves' },
      ]
    },
    chemistry: {
      chapters: [
        { id: '1', title: 'The Solid State', completed: true, duration: '2h 00m', videos: 9, description: 'Crystal structures and solid properties' },
        { id: '2', title: 'Solutions and Colligative Properties', completed: true, duration: '2h 30m', videos: 11, description: 'Solution chemistry and colligative properties' },
        { id: '3', title: 'Chemical Thermodynamics', completed: false, duration: '3h 00m', videos: 14, description: 'Energy changes in chemical reactions' },
        { id: '4', title: 'Electrochemistry', completed: false, duration: '2h 45m', videos: 12, description: 'Electrochemical cells and reactions' },
        { id: '5', title: 'Chemical Kinetics', completed: false, duration: '2h 15m', videos: 10, description: 'Rate of chemical reactions' },
        { id: '6', title: 'p-Block Elements', completed: false, duration: '3h 30m', videos: 16, description: 'Chemistry of p-block elements' },
      ]
    },
    mathematics: {
      chapters: [
        { id: '1', title: 'Mathematical Logic', completed: true, duration: '1h 30m', videos: 6, description: 'Logic statements and reasoning' },
        { id: '2', title: 'Matrices', completed: true, duration: '2h 45m', videos: 12, description: 'Matrix operations and applications' },
        { id: '3', title: 'Trigonometric Functions', completed: false, duration: '3h 00m', videos: 15, description: 'Advanced trigonometry and identities' },
        { id: '4', title: 'Integration', completed: false, duration: '4h 00m', videos: 20, description: 'Integration techniques and applications' },
        { id: '5', title: 'Differential Equations', completed: false, duration: '3h 30m', videos: 17, description: 'Solving differential equations' },
      ]
    },
    biology: {
      chapters: [
        { id: '1', title: 'Human Reproduction', completed: true, duration: '2h 30m', videos: 11, description: 'Human reproductive system and processes' },
        { id: '2', title: 'Genetics and Evolution', completed: false, duration: '3h 15m', videos: 16, description: 'Heredity, variation, and evolution' },
        { id: '3', title: 'Biotechnology', completed: false, duration: '2h 45m', videos: 13, description: 'Applications of biotechnology' },
        { id: '4', title: 'Human Health & Diseases', completed: false, duration: '2h 20m', videos: 10, description: 'Diseases and immune system' },
      ]
    },
    english: {
      chapters: [
        { id: '1', title: 'Prose and Poetry', completed: true, duration: '2h 00m', videos: 8, description: 'Literature analysis and comprehension' },
        { id: '2', title: 'Grammar Advanced', completed: false, duration: '1h 45m', videos: 7, description: 'Advanced grammar concepts' },
        { id: '3', title: 'Writing Skills', completed: false, duration: '2h 15m', videos: 9, description: 'Essay writing and composition' },
        { id: '4', title: 'Communication Skills', completed: false, duration: '1h 30m', videos: 6, description: 'Effective communication techniques' },
      ]
    }
  };

  const currentSubject = subjectData[id as keyof typeof subjectData] || subjectData.physics;
  const filteredChapters = currentSubject.chapters.filter(chapter =>
    chapter.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleChapterPress = (chapter: any) => {
    router.push(`/chapter-detail?subjectId=${id}&chapterId=${chapter.id}&title=${encodeURIComponent(chapter.title)}&color=${encodeURIComponent(color as string)}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: color as string }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.title}>{name}</Text>
          <Text style={styles.subtitle}>HSC Science • Class 12</Text>
        </View>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search chapters..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#9CA3AF"
        />
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'chapters' && [styles.activeTab, { borderBottomColor: color as string }]]}
          onPress={() => setActiveTab('chapters')}
        >
          <Text style={[styles.tabText, activeTab === 'chapters' && [styles.activeTabText, { color: color as string }]]}>
            Chapters
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'videos' && [styles.activeTab, { borderBottomColor: color as string }]]}
          onPress={() => setActiveTab('videos')}
        >
          <Text style={[styles.tabText, activeTab === 'videos' && [styles.activeTabText, { color: color as string }]]}>
            Videos
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'notes' && [styles.activeTab, { borderBottomColor: color as string }]]}
          onPress={() => setActiveTab('notes')}
        >
          <Text style={[styles.tabText, activeTab === 'notes' && [styles.activeTabText, { color: color as string }]]}>
            Notes
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'chapters' && (
          <>
            {filteredChapters.map((chapter) => (
              <TouchableOpacity
                key={chapter.id}
                style={styles.chapterCard}
                onPress={() => handleChapterPress(chapter)}
              >
                <View style={styles.chapterHeader}>
                  <View style={styles.chapterLeft}>
                    <View style={[styles.chapterIcon, { backgroundColor: `${color}20` }]}>
                      {chapter.completed ? (
                        <CheckCircle size={24} color={color as string} />
                      ) : (
                        <BookOpen size={24} color={color as string} />
                      )}
                    </View>
                    <View style={styles.chapterInfo}>
                      <Text style={styles.chapterTitle}>{chapter.title}</Text>
                      <Text style={styles.chapterDescription}>{chapter.description}</Text>
                      <View style={styles.chapterMeta}>
                        <Clock size={14} color="#6B7280" />
                        <Text style={styles.chapterMetaText}>{chapter.duration}</Text>
                        <Youtube size={14} color="#6B7280" />
                        <Text style={styles.chapterMetaText}>{chapter.videos} videos</Text>
                      </View>
                    </View>
                  </View>
                  <Play size={20} color="#9CA3AF" />
                </View>
                
                {chapter.completed && (
                  <View style={styles.completedBadge}>
                    <CheckCircle size={16} color="#10B981" />
                    <Text style={styles.completedText}>Completed</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </>
        )}

        {activeTab === 'videos' && (
          <View style={styles.comingSoon}>
            <Youtube size={64} color="#9CA3AF" />
            <Text style={styles.comingSoonTitle}>Video Lectures</Text>
            <Text style={styles.comingSoonText}>
              Comprehensive video lectures for each chapter coming soon!
            </Text>
          </View>
        )}

        {activeTab === 'notes' && (
          <View style={styles.comingSoon}>
            <BookOpen size={64} color="#9CA3AF" />
            <Text style={styles.comingSoonTitle}>Study Notes</Text>
            <Text style={styles.comingSoonText}>
              Detailed notes and study materials will be available here.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 3,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginVertical: 16,
    borderRadius: 12,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  activeTabText: {
    fontFamily: 'Inter-SemiBold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  chapterCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  chapterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chapterLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  chapterIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  chapterInfo: {
    flex: 1,
  },
  chapterTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  chapterDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
  },
  chapterMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chapterMetaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
    marginRight: 12,
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginTop: 12,
    alignSelf: 'flex-start',
  },
  completedText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#10B981',
    marginLeft: 4,
  },
  comingSoon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  comingSoonTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  comingSoonText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 24,
  },
});