import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Dimensions } from 'react-native';
import { Play, Youtube, ExternalLink, Clock } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface VideoThumbnailProps {
  video: {
    id: string;
    title: string;
    channelTitle: string;
    thumbnail: string;
    duration?: string;
    viewCount?: string;
  };
  onPlayInApp: (videoId: string, title: string) => void;
  onOpenExternal: (videoId: string) => void;
}

const { width: screenWidth } = Dimensions.get('window');
const thumbnailWidth = screenWidth - 40; // Account for padding
const thumbnailHeight = (thumbnailWidth * 9) / 16; // 16:9 aspect ratio

export default function VideoThumbnail({ video, onPlayInApp, onOpenExternal }: VideoThumbnailProps) {
  return (
    <View style={styles.container}>
      {/* Thumbnail with Play Button */}
      <TouchableOpacity
        style={styles.thumbnailContainer}
        onPress={() => onPlayInApp(video.id, video.title)}
        activeOpacity={0.9}
      >
        <Image
          source={{ uri: video.thumbnail }}
          style={styles.thumbnail}
          resizeMode="cover"
        />

        {/* Gradient Overlay */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.3)']}
          style={styles.gradientOverlay}
        />

        {/* Play Button with Pulse Animation */}
        <View style={styles.playOverlay}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.playButton}
          >
            <Play size={28} color="#FFFFFF" fill="#FFFFFF" />
          </LinearGradient>
        </View>

        {/* Duration Badge */}
        {video.duration && video.duration !== 'N/A' && (
          <View style={styles.durationBadge}>
            <Clock size={12} color="#FFFFFF" />
            <Text style={styles.durationText}>{video.duration}</Text>
          </View>
        )}

        {/* Quality Badge */}
        <View style={styles.qualityBadge}>
          <Text style={styles.qualityText}>HD</Text>
        </View>
      </TouchableOpacity>

      {/* Video Info */}
      <View style={styles.videoInfo}>
        <View style={styles.videoDetails}>
          <Text style={styles.videoTitle} numberOfLines={2}>
            {video.title}
          </Text>
          <View style={styles.videoMeta}>
            <View style={styles.channelInfo}>
              <View style={styles.youtubeIcon}>
                <Youtube size={12} color="#FF0000" />
              </View>
              <Text style={styles.channelName} numberOfLines={1}>
                {video.channelTitle}
              </Text>
            </View>
            {video.viewCount && video.viewCount !== 'N/A' && (
              <View style={styles.viewInfo}>
                <View style={styles.viewDot} />
                <Text style={styles.viewCount}>{video.viewCount} views</Text>
              </View>
            )}
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onPlayInApp(video.id, video.title)}
          >
            <Play size={16} color="#667eea" fill="#667eea" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onOpenExternal(video.id)}
          >
            <ExternalLink size={16} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  thumbnailContainer: {
    position: 'relative',
    width: '100%',
    height: thumbnailHeight,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: '#F3F4F6',
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  playOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    borderRadius: 35,
    width: 70,
    height: 70,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  durationBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  durationText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  qualityBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(102, 126, 234, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  qualityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontFamily: 'Inter-Bold',
  },
  videoInfo: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'flex-start',
  },
  videoDetails: {
    flex: 1,
    marginRight: 16,
  },
  videoTitle: {
    fontSize: 17,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    lineHeight: 24,
    marginBottom: 10,
  },
  videoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  channelInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  youtubeIcon: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 4,
    marginRight: 8,
  },
  channelName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    flex: 1,
  },
  viewInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewDot: {
    width: 3,
    height: 3,
    borderRadius: 1.5,
    backgroundColor: '#9CA3AF',
    marginRight: 6,
  },
  viewCount: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: '#F8FAFC',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
});
