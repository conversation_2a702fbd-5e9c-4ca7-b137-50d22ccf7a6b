import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Linking, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Play, Youtube, BookOpen, CircleHelp as HelpCircle, ExternalLink } from 'lucide-react-native';
import VideoThumbnail from '@/components/VideoThumbnail';

interface YouTubeVideo {
  id: string;
  title: string;
  channelTitle: string;
  duration: string;
  viewCount: string;
  thumbnail: string;
}

export default function ChapterDetailScreen() {
  const { subjectId, chapterId, title, color } = useLocalSearchParams();
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchYouTubeVideos();
  }, [title]);

  const fetchYouTubeVideos = async () => {
    try {
      const API_KEY = process.env.EXPO_PUBLIC_YOUTUBE_API_KEY;
      if (!API_KEY) {
        console.error('YouTube API key not found');
        setLoading(false);
        return;
      }

      const searchQuery = `${title} Maharashtra HSC Science Class 12`;
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&maxResults=10&q=${encodeURIComponent(searchQuery)}&type=video&key=${API_KEY}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.items) {
        const formattedVideos: YouTubeVideo[] = data.items.map((item: any) => ({
          id: item.id.videoId,
          title: item.snippet.title,
          channelTitle: item.snippet.channelTitle,
          duration: 'N/A', // YouTube API v3 doesn't include duration in search
          viewCount: 'N/A',
          thumbnail: item.snippet.thumbnails.medium.url,
        }));
        setVideos(formattedVideos);
      }
    } catch (error) {
      console.error('Error fetching YouTube videos:', error);
      Alert.alert('Error', 'Failed to load videos. Please check your internet connection.');
    } finally {
      setLoading(false);
    }
  };

  const openYouTubeVideo = (videoId: string) => {
    const url = `https://www.youtube.com/watch?v=${videoId}`;
    Linking.openURL(url).catch((err) => {
      console.error('Error opening YouTube video:', err);
      Alert.alert('Error', 'Could not open video');
    });
  };

  const playVideoInApp = (videoId: string, title: string) => {
    router.push({
      pathname: '/video-player',
      params: { videoId, title }
    });
  };

  const askAI = () => {
    router.push({
      pathname: '/(tabs)/ai-tutor',
      params: { initialMessage: `Help me understand ${title}` }
    });
  };

  const chapterContent = {
    overview: "This chapter covers fundamental concepts and principles that form the foundation of understanding in this subject area.",
    keyTopics: [
      "Basic concepts and definitions",
      "Mathematical formulations and derivations",
      "Practical applications and examples",
      "Problem-solving techniques",
      "Important formulas and equations"
    ],
    practiceQuestions: [
      "What are the fundamental principles covered in this chapter?",
      "How do you apply these concepts to solve real-world problems?",
      "What are the key formulas and their applications?",
      "Explain the relationship between different concepts in this chapter."
    ]
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: color as string }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>Chapter Content & Resources</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: `${color}20` }]}
              onPress={askAI}
            >
              <HelpCircle size={24} color={color as string} />
              <Text style={[styles.actionButtonText, { color: color as string }]}>Ask AI Tutor</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#FEF3C7' }]}
              onPress={() => router.push('/(tabs)/practice')}
            >
              <BookOpen size={24} color="#F59E0B" />
              <Text style={[styles.actionButtonText, { color: '#F59E0B' }]}>Take Quiz</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Chapter Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Chapter Overview</Text>
          <View style={styles.overviewCard}>
            <Text style={styles.overviewText}>{chapterContent.overview}</Text>
          </View>
        </View>

        {/* Key Topics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Topics</Text>
          {chapterContent.keyTopics.map((topic, index) => (
            <View key={index} style={styles.topicItem}>
              <View style={[styles.topicDot, { backgroundColor: color as string }]} />
              <Text style={styles.topicText}>{topic}</Text>
            </View>
          ))}
        </View>

        {/* YouTube Videos */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📺 Video Lectures</Text>
            <View style={styles.videoBadge}>
              <Text style={styles.videoBadgeText}>{videos.length} videos</Text>
            </View>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#667eea" />
              <Text style={styles.loadingText}>Finding best videos for you...</Text>
            </View>
          ) : videos.length > 0 ? (
            <View style={styles.videosContainer}>
              {videos.map((video, index) => (
                <VideoThumbnail
                  key={video.id}
                  video={video}
                  onPlayInApp={playVideoInApp}
                  onOpenExternal={openYouTubeVideo}
                />
              ))}
            </View>
          ) : (
            <View style={styles.noVideosContainer}>
              <View style={styles.noVideosIcon}>
                <Youtube size={48} color="#667eea" />
              </View>
              <Text style={styles.noVideosTitle}>No videos found</Text>
              <Text style={styles.noVideosText}>
                We couldn't find any videos for this topic. Try searching on YouTube directly.
              </Text>
            </View>
          )}
        </View>

        {/* Practice Questions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Practice Questions</Text>
          {chapterContent.practiceQuestions.map((question, index) => (
            <TouchableOpacity key={index} style={styles.questionCard}>
              <Text style={styles.questionNumber}>Q{index + 1}</Text>
              <Text style={styles.questionText}>{question}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Study Tips */}
        <View style={styles.section}>
          <View style={styles.tipsCard}>
            <Text style={styles.tipsTitle}>💡 Study Tips</Text>
            <Text style={styles.tipsText}>
              • Start with understanding basic concepts before moving to complex problems{'\n'}
              • Practice numerical problems regularly{'\n'}
              • Use diagrams and flowcharts for better visualization{'\n'}
              • Ask your AI tutor for clarification on difficult topics
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 3,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  videoBadge: {
    backgroundColor: '#667eea',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  videoBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  videosContainer: {
    gap: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginLeft: 8,
  },
  overviewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  overviewText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  topicItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  topicDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  topicText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    flex: 1,
  },
  loadingContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 48,
    alignItems: 'center',
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 16,
  },

  noVideosContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 48,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  noVideosIcon: {
    backgroundColor: '#F0F4FF',
    borderRadius: 32,
    padding: 16,
    marginBottom: 16,
  },
  noVideosTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  noVideosText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
  questionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  questionNumber: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#3B82F6',
    marginRight: 12,
    minWidth: 32,
  },
  questionText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    flex: 1,
    lineHeight: 24,
  },
  tipsCard: {
    backgroundColor: '#FEF3C7',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  tipsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#92400E',
    marginBottom: 12,
  },
  tipsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#92400E',
    lineHeight: 22,
  },
});