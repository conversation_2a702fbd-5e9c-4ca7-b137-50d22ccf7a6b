import { Alert } from 'react-native';

// API key should be set in .env file as EXPO_PUBLIC_GEMINI_API_KEY

export interface AIQuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  topic: string;
  hintsInMarathi?: string;
}

export interface QuizGenerationParams {
  subject: string;
  topic: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  questionCount: number;
  includeMarathiHints?: boolean;
  studentLevel?: 'weak' | 'average' | 'good';
}

export class AIQuizGenerator {
  private static async callGeminiAPI(prompt: string): Promise<string> {
    try {
      const API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
      if (!API_KEY || API_KEY === 'YOUR_GEMINI_API_KEY_HERE' || API_KEY.trim() === '') {
        throw new Error('API_KEY_MISSING');
      }

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
        return data.candidates[0].content.parts[0].text;
      } else {
        console.error('Invalid API response:', data);
        throw new Error('Invalid response format from Gemini API');
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      if (error instanceof Error && error.message === 'API_KEY_MISSING') {
        throw error;
      }
      throw new Error('Failed to connect to AI service');
    }
  }

  static async generateQuiz(params: QuizGenerationParams): Promise<AIQuizQuestion[]> {
    const { subject, topic, difficulty, questionCount, includeMarathiHints, studentLevel } = params;

    const difficultyMapping = {
      'Beginner': 'Easy to Medium',
      'Intermediate': 'Medium',
      'Advanced': 'Medium to Hard'
    };

    const studentLevelContext = {
      'weak': 'The student has poor study habits and lacks basic knowledge. Use simple language and provide extra explanations.',
      'average': 'The student has average understanding. Provide clear explanations with moderate complexity.',
      'good': 'The student has good understanding. You can use more complex concepts and advanced explanations.'
    };

    const marathiHintInstruction = includeMarathiHints
      ? 'Also provide hints in Marathi language for better understanding since the student is a native Marathi speaker.'
      : '';

    const prompt = `
Generate ${questionCount} multiple choice questions for Maharashtra HSC Class 12 ${subject} on the topic "${topic}".

Student Context: ${studentLevelContext[studentLevel || 'average']}
Difficulty Level: ${difficultyMapping[difficulty]}
${marathiHintInstruction}

Requirements:
1. Questions should be based on Maharashtra State Board HSC syllabus 2025-26
2. Each question should have exactly 4 options (A, B, C, D)
3. Provide detailed explanations for correct answers
4. Include the specific subtopic for each question
5. Make questions practical and application-based where possible
6. Ensure questions test conceptual understanding, not just memorization

${includeMarathiHints ? `
7. For each question, provide a hint in Marathi that helps the student understand the concept better
8. Use simple Marathi that a Class 12 student would understand
` : ''}

Format your response as a valid JSON array with this exact structure:
[
  {
    "question": "Question text here",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correctAnswer": 0,
    "explanation": "Detailed explanation of why this answer is correct",
    "difficulty": "Easy|Medium|Hard",
    "topic": "${topic}",
    ${includeMarathiHints ? '"hintsInMarathi": "Marathi hint here",' : ''}
    "subtopic": "Specific subtopic name"
  }
]

Subject: ${subject}
Topic: ${topic}
Generate exactly ${questionCount} questions now:`;

    try {
      const response = await this.callGeminiAPI(prompt);

      // Clean the response and extract JSON
      let cleanResponse = response.trim();

      // Remove markdown code blocks if present
      cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Extract JSON from response
      const jsonMatch = cleanResponse.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        console.error('No JSON array found in response:', cleanResponse);
        throw new Error('No valid JSON found in response');
      }

      let questionsData;
      try {
        questionsData = JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        console.error('Attempted to parse:', jsonMatch[0]);
        throw new Error('Invalid JSON format in response');
      }

      // Validate the data structure
      if (!Array.isArray(questionsData)) {
        throw new Error('Response is not an array');
      }

      // Convert to our format and add IDs
      const questions: AIQuizQuestion[] = questionsData.map((q: any, index: number) => ({
        id: `ai_${Date.now()}_${index}`,
        question: q.question || 'Sample question',
        options: Array.isArray(q.options) ? q.options : ['Option A', 'Option B', 'Option C', 'Option D'],
        correctAnswer: typeof q.correctAnswer === 'number' ? q.correctAnswer : 0,
        explanation: q.explanation || 'Explanation not available',
        difficulty: q.difficulty || 'Medium',
        topic: q.topic || topic,
        hintsInMarathi: q.hintsInMarathi || undefined,
      }));

      return questions.length > 0 ? questions : this.getFallbackQuestions(subject, topic, questionCount);
    } catch (error) {
      console.error('Error generating quiz:', error);

      // Always return fallback questions if AI fails
      return this.getFallbackQuestions(subject, topic, questionCount);
    }
  }

  private static getFallbackQuestions(subject: string, topic: string, count: number): AIQuizQuestion[] {
    const fallbackQuestions: { [key: string]: AIQuizQuestion[] } = {
      'English': [
        {
          id: 'fallback_eng_1',
          question: 'Choose the correct sentence:',
          options: ['I am go to school', 'I go to school', 'I going to school', 'I goes to school'],
          correctAnswer: 1,
          explanation: 'Simple present tense uses the base form of the verb with "I".',
          difficulty: 'Easy',
          topic: 'Grammar',
          hintsInMarathi: 'साधा वर्तमान काळ वापरा - "I" सोबत मूळ क्रियापद वापरतात'
        },
        {
          id: 'fallback_eng_2',
          question: 'What is the plural of "child"?',
          options: ['childs', 'children', 'childes', 'child'],
          correctAnswer: 1,
          explanation: '"Child" is an irregular noun. Its plural form is "children".',
          difficulty: 'Easy',
          topic: 'Grammar',
          hintsInMarathi: '"Child" हे अनियमित नाम आहे - त्याचे अनेकवचन "children" होते'
        }
      ],
      'Physics': [
        {
          id: 'fallback_phy_1',
          question: 'What is the SI unit of force?',
          options: ['Joule', 'Newton', 'Watt', 'Pascal'],
          correctAnswer: 1,
          explanation: 'Newton (N) is the SI unit of force. 1 N = 1 kg⋅m⋅s⁻²',
          difficulty: 'Easy',
          topic: 'Mechanics',
          hintsInMarathi: 'बल मोजण्याचे SI एकक न्यूटन आहे'
        }
      ],
      'Chemistry': [
        {
          id: 'fallback_chem_1',
          question: 'What is the chemical formula for water?',
          options: ['H₂O', 'HO₂', 'H₂O₂', 'OH₂'],
          correctAnswer: 0,
          explanation: 'Water consists of 2 hydrogen atoms and 1 oxygen atom, so its formula is H₂O.',
          difficulty: 'Easy',
          topic: 'Chemical Formulas',
          hintsInMarathi: 'पाण्यात 2 हायड्रोजन आणि 1 ऑक्सिजन अणू असतात'
        }
      ],
      'Mathematics': [
        {
          id: 'fallback_math_1',
          question: 'What is 2 + 2 × 3?',
          options: ['12', '8', '10', '6'],
          correctAnswer: 1,
          explanation: 'Following BODMAS rule: 2 + (2 × 3) = 2 + 6 = 8',
          difficulty: 'Easy',
          topic: 'Basic Operations',
          hintsInMarathi: 'BODMAS नियम वापरा - आधी गुणाकार, नंतर बेरीज'
        }
      ],
      'Biology': [
        {
          id: 'fallback_bio_1',
          question: 'What is the basic unit of life?',
          options: ['Tissue', 'Organ', 'Cell', 'Organism'],
          correctAnswer: 2,
          explanation: 'Cell is the smallest structural and functional unit of life.',
          difficulty: 'Easy',
          topic: 'Cell Biology',
          hintsInMarathi: 'पेशी ही जीवनाची सर्वात लहान एकक आहे'
        }
      ]
    };

    const subjectQuestions = fallbackQuestions[subject] || fallbackQuestions['English'];
    return subjectQuestions.slice(0, count);
  }

  static async generatePersonalizedQuiz(
    subject: string,
    topic: string,
    studentWeakAreas: string[],
    previousScores: number[]
  ): Promise<AIQuizQuestion[]> {
    const averageScore = previousScores.length > 0
      ? previousScores.reduce((a, b) => a + b, 0) / previousScores.length
      : 50;

    let difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
    let studentLevel: 'weak' | 'average' | 'good';

    if (averageScore < 40) {
      difficulty = 'Beginner';
      studentLevel = 'weak';
    } else if (averageScore < 70) {
      difficulty = 'Intermediate';
      studentLevel = 'average';
    } else {
      difficulty = 'Advanced';
      studentLevel = 'good';
    }

    return this.generateQuiz({
      subject,
      topic,
      difficulty,
      questionCount: 10,
      includeMarathiHints: true,
      studentLevel
    });
  }
}
