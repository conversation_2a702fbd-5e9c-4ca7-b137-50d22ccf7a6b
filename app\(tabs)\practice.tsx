import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Animated, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Target, Clock, Trophy, Star, Play, CircleCheck as CheckCircle, Circle as XCircle, Brain, Zap, Award } from 'lucide-react-native';
import { router } from 'expo-router';
import { useQuizResults } from '@/hooks/useQuizResults';
import { QuizResult } from '@/lib/storage';
import { useStudyProgress } from '@/hooks/useStudyProgress';

const { width } = Dimensions.get('window');

interface QuizQuestion {
  id: string;
  subject: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
}

interface Quiz {
  id: string;
  title: string;
  subject: string;
  duration: number;
  questions: number;
  difficulty: 'Easy' | 'Medium' | 'Hard' | 'Start Here' | 'Beginner' | 'Intermediate' | 'Advanced';
  color: string;
  icon: string;
}

export default function PracticeScreen() {
  const { results, saveQuizResult } = useQuizResults();
  const { updateProgress } = useStudyProgress();
  const [activeQuiz, setActiveQuiz] = useState<Quiz | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [score, setScore] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));
  const [progressAnim] = useState(new Animated.Value(0));

  const quizzes: Quiz[] = [
    {
      id: '1',
      title: 'English Foundation Quiz',
      subject: 'English',
      duration: 10,
      questions: 10,
      difficulty: 'Start Here',
      color: '#EF4444',
      icon: '🗣️',
    },
    {
      id: '2',
      title: 'Basic Physics Concepts',
      subject: 'Physics',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#3B82F6',
      icon: '⚛️',
    },
    {
      id: '3',
      title: 'Chemistry Basics',
      subject: 'Chemistry',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#10B981',
      icon: '🧪',
    },
    {
      id: '4',
      title: 'Math Fundamentals',
      subject: 'Mathematics',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#F59E0B',
      icon: '📐',
    },
    {
      id: '5',
      title: 'Biology Basics',
      subject: 'Biology',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#8B5CF6',
      icon: '🧬',
    },
  ];

  // Real quiz questions based on Maharashtra HSC syllabus
  const getQuizQuestions = (quizId: string): QuizQuestion[] => {
    const questionBank: { [key: string]: QuizQuestion[] } = {
      '1': [ // English Foundation Quiz
        {
          id: '1',
          subject: 'English',
          question: 'Choose the correct sentence:',
          options: ['I am go to school', 'I go to school', 'I going to school', 'I goes to school'],
          correctAnswer: 1,
          explanation: 'Simple present tense: Subject + base form of verb',
          difficulty: 'Start Here',
        },
        {
          id: '2',
          subject: 'English',
          question: 'What is the plural of "child"?',
          options: ['childs', 'children', 'childes', 'child'],
          correctAnswer: 1,
          explanation: 'Child is an irregular noun. Its plural is children.',
          difficulty: 'Start Here',
        },
        {
          id: '3',
          subject: 'English',
          question: 'Choose the correct article: ___ apple',
          options: ['a', 'an', 'the', 'no article'],
          correctAnswer: 1,
          explanation: 'Use "an" before words starting with vowel sounds.',
          difficulty: 'Start Here',
        },
      ],
      '2': [ // Basic Physics Concepts
        {
          id: '1',
          subject: 'Physics',
          question: 'What is the SI unit of force?',
          options: ['Joule', 'Newton', 'Watt', 'Pascal'],
          correctAnswer: 1,
          explanation: 'Newton (N) is the SI unit of force. 1 N = 1 kg⋅m⋅s⁻²',
          difficulty: 'Beginner',
        },
        {
          id: '2',
          subject: 'Physics',
          question: 'Which law states "Every action has an equal and opposite reaction"?',
          options: ['First law of motion', 'Second law of motion', 'Third law of motion', 'Law of gravitation'],
          correctAnswer: 2,
          explanation: 'Newton\'s third law of motion states this principle.',
          difficulty: 'Beginner',
        },
      ],
      '3': [ // Chemistry Basics
        {
          id: '1',
          subject: 'Chemistry',
          question: 'What is the chemical formula for water?',
          options: ['H₂O', 'HO₂', 'H₂O₂', 'OH₂'],
          correctAnswer: 0,
          explanation: 'Water consists of 2 hydrogen atoms and 1 oxygen atom.',
          difficulty: 'Beginner',
        },
      ],
      '4': [ // Math Fundamentals
        {
          id: '1',
          subject: 'Mathematics',
          question: 'What is 2 + 2 × 3?',
          options: ['12', '8', '10', '6'],
          correctAnswer: 1,
          explanation: 'Follow BODMAS: 2 + (2 × 3) = 2 + 6 = 8',
          difficulty: 'Beginner',
        },
      ],
      '5': [ // Biology Basics
        {
          id: '1',
          subject: 'Biology',
          question: 'What is the basic unit of life?',
          options: ['Tissue', 'Organ', 'Cell', 'Organism'],
          correctAnswer: 2,
          explanation: 'Cell is the smallest structural and functional unit of life.',
          difficulty: 'Beginner',
        },
      ],
    };

    return questionBank[quizId] || [];
  };

  const sampleQuestions = getQuizQuestions(activeQuiz?.id || '1');

  const calculateStats = () => {
    const totalCompleted = results.length;
    const averageScore = results.length > 0
      ? Math.round(results.reduce((sum, result) => sum + (result.score / result.total_questions * 100), 0) / results.length)
      : 0;
    const totalTime = results.reduce((sum, result) => sum + 15, 0); // Assuming 15 min average per quiz

    return {
      totalCompleted,
      averageScore,
      totalTime: `${Math.floor(totalTime / 60)}h ${totalTime % 60}m`,
    };
  };

  const stats = calculateStats();

  const startQuiz = (quiz: Quiz) => {
    // Check if API key is available for AI features
    const hasApiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY &&
                     process.env.EXPO_PUBLIC_GEMINI_API_KEY !== 'YOUR_GEMINI_API_KEY_HERE';

    if (hasApiKey) {
      // Navigate to AI-powered quiz selection
      router.push({
        pathname: '/quiz-selection',
        params: {
          subject: quiz.subject
        }
      });
    } else {
      // Show basic quiz with fallback questions
      Alert.alert(
        'Quiz Mode',
        'AI features require an API key. Would you like to:\n\n• Use basic quiz mode (available now)\n• Set up AI features for personalized quizzes',
        [
          { text: 'Basic Quiz', onPress: () => startBasicQuiz(quiz) },
          { text: 'Setup AI', onPress: () => showApiKeyInstructions() },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    }
  };

  const startBasicQuiz = (quiz: Quiz) => {
    setActiveQuiz(quiz);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setScore(0);
    setShowResult(false);
    setQuizCompleted(false);
  };

  const showApiKeyInstructions = () => {
    Alert.alert(
      'Setup AI Features',
      'To enable AI-powered quizzes and personalized learning:\n\n1. Get a free API key from Google AI Studio\n2. Add it to your .env file as EXPO_PUBLIC_GEMINI_API_KEY\n3. Restart the app\n\nVisit: https://makersuite.google.com/app/apikey',
      [{ text: 'OK' }]
    );
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const submitAnswer = () => {
    if (selectedAnswer === null) return;

    const currentQuestion = sampleQuestions[currentQuestionIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;

    if (isCorrect) {
      setScore(score + 1);
    }

    setShowResult(true);

    setTimeout(() => {
      if (currentQuestionIndex < sampleQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        setSelectedAnswer(null);
        setShowResult(false);
      } else {
        // Quiz completed
        setQuizCompleted(true);
      }
    }, 2000);
  };

  const finishQuiz = async () => {
    if (activeQuiz) {
      const percentage = Math.round((score / sampleQuestions.length) * 100);
      const timeSpent = activeQuiz.duration; // Use quiz duration as time spent

      // Create quiz result object
      const quizResult: QuizResult = {
        id: Date.now().toString(),
        quiz_title: activeQuiz.title,
        subject: activeQuiz.subject,
        score,
        total_questions: sampleQuestions.length,
        time_taken: timeSpent * 60, // Convert to seconds
        completed_at: new Date().toISOString(),
        answers: [], // We'll add this later if needed
      };

      // Save quiz result
      await saveQuizResult(quizResult);

      // Update progress
      await updateProgress(activeQuiz.subject, activeQuiz.title, percentage >= 70, percentage, timeSpent);
    }

    setActiveQuiz(null);
    setQuizCompleted(false);

    const percentage = Math.round((score / sampleQuestions.length) * 100);
    const message = percentage >= 70
      ? `Excellent! You scored ${score}/${sampleQuestions.length} (${percentage}%)\n\nYou can move to the next topic!`
      : `You scored ${score}/${sampleQuestions.length} (${percentage}%)\n\nTry to score 70% or above to unlock next topics.`;

    Alert.alert(
      'Quiz Completed!',
      message,
      [{ text: 'OK' }]
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return '#10B981';
      case 'Medium': return '#F59E0B';
      case 'Hard': return '#EF4444';
      default: return '#6B7280';
    }
  };

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  React.useEffect(() => {
    if (activeQuiz) {
      const progress = ((currentQuestionIndex + 1) / sampleQuestions.length) * 100;
      Animated.timing(progressAnim, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [currentQuestionIndex, activeQuiz]);

  if (activeQuiz && !quizCompleted) {
    const currentQuestion = sampleQuestions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / sampleQuestions.length) * 100;

    return (
      <SafeAreaView style={styles.container}>
        {/* Quiz Header */}
        <LinearGradient
          colors={[activeQuiz.color, activeQuiz.color + 'CC']}
          style={styles.quizHeader}
        >
          <View style={styles.quizProgress}>
            <Text style={styles.questionCounter}>
              Question {currentQuestionIndex + 1} of {sampleQuestions.length}
            </Text>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBar}>
                <Animated.View
                  style={[
                    styles.progressFill,
                    {
                      width: progressAnim.interpolate({
                        inputRange: [0, 100],
                        outputRange: ['0%', '100%'],
                      }),
                      backgroundColor: '#FFFFFF'
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>{Math.round(progress)}%</Text>
            </View>
          </View>
          <Text style={styles.quizTitle}>{activeQuiz.title}</Text>
        </LinearGradient>

        {/* Question */}
        <ScrollView style={styles.questionContainer}>
          <Text style={styles.questionText}>{currentQuestion.question}</Text>

          {/* Options */}
          <View style={styles.optionsContainer}>
            {currentQuestion.options.map((option, index) => {
              let optionStyle: any = styles.option;
              let textStyle: any = styles.optionText;

              if (showResult) {
                if (index === currentQuestion.correctAnswer) {
                  optionStyle = [styles.option, styles.correctOption];
                  textStyle = [styles.optionText, styles.correctOptionText];
                } else if (index === selectedAnswer) {
                  optionStyle = [styles.option, styles.wrongOption];
                  textStyle = [styles.optionText, styles.wrongOptionText];
                }
              } else if (selectedAnswer === index) {
                optionStyle = [styles.option, styles.selectedOption];
                textStyle = [styles.optionText, styles.selectedOptionText];
              }

              return (
                <TouchableOpacity
                  key={index}
                  style={optionStyle}
                  onPress={() => handleAnswerSelect(index)}
                  disabled={showResult}
                >
                  <Text style={textStyle}>{option}</Text>
                  {showResult && index === currentQuestion.correctAnswer && (
                    <CheckCircle size={20} color="#10B981" />
                  )}
                  {showResult && index === selectedAnswer && index !== currentQuestion.correctAnswer && (
                    <XCircle size={20} color="#EF4444" />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Explanation */}
          {showResult && (
            <View style={styles.explanationContainer}>
              <Text style={styles.explanationTitle}>Explanation:</Text>
              <Text style={styles.explanationText}>{currentQuestion.explanation}</Text>
            </View>
          )}
        </ScrollView>

        {/* Submit Button */}
        {!showResult && (
          <TouchableOpacity
            style={[styles.submitButton, selectedAnswer === null && styles.submitButtonDisabled]}
            onPress={submitAnswer}
            disabled={selectedAnswer === null}
          >
            <Text style={styles.submitButtonText}>Submit Answer</Text>
          </TouchableOpacity>
        )}
      </SafeAreaView>
    );
  }

  if (quizCompleted) {
    const percentage = Math.round((score / sampleQuestions.length) * 100);
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.resultContainer}>
          <Trophy size={64} color="#F59E0B" />
          <Text style={styles.resultTitle}>Quiz Completed!</Text>
          <Text style={styles.resultScore}>{score}/{sampleQuestions.length}</Text>
          <Text style={styles.resultPercentage}>{percentage}%</Text>

          <TouchableOpacity style={styles.finishButton} onPress={finishQuiz}>
            <Text style={styles.finishButtonText}>Finish</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <Animated.View style={[styles.header, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Brain size={28} color="#FFFFFF" />
            </View>
            <View>
              <Text style={styles.title}>Practice Tests</Text>
              <Text style={styles.subtitle}>Test your knowledge and track progress</Text>
            </View>
          </View>
        </Animated.View>
      </LinearGradient>

      {/* Stats */}
      <Animated.View style={[styles.statsContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
        <LinearGradient
          colors={['#3B82F6', '#1E40AF']}
          style={styles.statCard}
        >
          <View style={styles.statIconContainer}>
            <Target size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.statValue}>{stats.totalCompleted}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </LinearGradient>

        <LinearGradient
          colors={['#F59E0B', '#D97706']}
          style={styles.statCard}
        >
          <View style={styles.statIconContainer}>
            <Star size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.statValue}>{stats.averageScore}%</Text>
          <Text style={styles.statLabel}>Avg Score</Text>
        </LinearGradient>

        <LinearGradient
          colors={['#10B981', '#059669']}
          style={styles.statCard}
        >
          <View style={styles.statIconContainer}>
            <Clock size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.statValue}>{stats.totalTime}</Text>
          <Text style={styles.statLabel}>Time Spent</Text>
        </LinearGradient>
      </Animated.View>

      {/* Quizzes */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}>
          <Text style={styles.sectionTitle}>Available Quizzes</Text>
        </Animated.View>

        {quizzes.map((quiz) => {
          const quizResult = results.find(r => r.quiz_title === quiz.title);
          const isCompleted = !!quizResult;
          const score = quizResult ? Math.round((quizResult.score / quizResult.total_questions) * 100) : 0;

          return (
            <Animated.View
              key={quiz.id}
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }}
            >
              <TouchableOpacity
                style={[
                  styles.quizCard,
                  isCompleted && styles.completedQuizCard
                ]}
                onPress={() => startQuiz(quiz)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[quiz.color + '08', quiz.color + '03']}
                  style={styles.cardGradient}
                >
                  {/* Card Header */}
                  <View style={styles.cardHeader}>
                    <View style={styles.cardLeft}>
                      <View style={[styles.iconContainer, { backgroundColor: quiz.color + '20' }]}>
                        <Text style={styles.quizIcon}>{quiz.icon}</Text>
                      </View>
                      <View style={styles.titleContainer}>
                        <Text style={styles.quizTitle}>{quiz.title}</Text>
                        <Text style={styles.quizSubject}>{quiz.subject}</Text>
                      </View>
                    </View>

                    {isCompleted && (
                      <View style={styles.completionIndicator}>
                        <CheckCircle size={20} color="#10B981" />
                      </View>
                    )}
                  </View>

                  {/* Progress Bar for Completed Quizzes */}
                  {isCompleted && (
                    <View style={styles.progressSection}>
                      <View style={styles.progressInfo}>
                        <Text style={styles.progressLabel}>Best Score</Text>
                        <Text style={[styles.progressScore, { color: score >= 70 ? '#10B981' : score >= 50 ? '#F59E0B' : '#EF4444' }]}>
                          {score}%
                        </Text>
                      </View>
                      <View style={styles.progressBarContainer}>
                        <View style={styles.progressBar}>
                          <View
                            style={[
                              styles.progressFill,
                              {
                                width: `${score}%`,
                                backgroundColor: score >= 70 ? '#10B981' : score >= 50 ? '#F59E0B' : '#EF4444'
                              }
                            ]}
                          />
                        </View>
                      </View>
                    </View>
                  )}

                  {/* Quiz Metadata */}
                  <View style={styles.cardMeta}>
                    <View style={styles.metaItem}>
                      <Clock size={14} color="#6B7280" />
                      <Text style={styles.metaText}>{quiz.duration} min</Text>
                    </View>
                    <View style={styles.metaItem}>
                      <Target size={14} color="#6B7280" />
                      <Text style={styles.metaText}>{quiz.questions} questions</Text>
                    </View>
                    <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(quiz.difficulty) + '20' }]}>
                      <Text style={[styles.difficultyText, { color: getDifficultyColor(quiz.difficulty) }]}>
                        {quiz.difficulty}
                      </Text>
                    </View>
                  </View>

                  {/* Action Button */}
                  <TouchableOpacity
                    style={[styles.startButton, { backgroundColor: quiz.color }]}
                    onPress={() => startQuiz(quiz)}
                  >
                    <LinearGradient
                      colors={[quiz.color, quiz.color + 'DD']}
                      style={styles.buttonGradient}
                    >
                      {isCompleted ? (
                        <>
                          <Trophy size={18} color="#FFFFFF" />
                          <Text style={styles.startButtonText}>Retake Quiz</Text>
                        </>
                      ) : (
                        <>
                          <Play size={18} color="#FFFFFF" />
                          <Text style={styles.startButtonText}>Start Quiz</Text>
                        </>
                      )}
                    </LinearGradient>
                  </TouchableOpacity>

                  {/* AI Badge */}
                  <View style={styles.aiBadge}>
                    <Brain size={12} color="#667eea" />
                    <Text style={styles.aiText}>AI Powered</Text>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          );
        })}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingBottom: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginTop: -12,
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    borderRadius: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 22,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 20,
  },
  quizCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    overflow: 'hidden',
  },
  quizContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quizLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  quizIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  quizIconText: {
    fontSize: 24,
  },
  quizInfo: {
    flex: 1,
  },
  quizCardTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  quizSubject: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  quizMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  quizMetaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginRight: 8,
  },
  quizRight: {
    alignItems: 'flex-end',
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  difficultyText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  completedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completedText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#10B981',
    marginLeft: 4,
  },
  // Quiz screen styles
  quizHeader: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 3,
  },
  quizProgress: {
    marginBottom: 12,
  },
  questionCounter: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 8,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  quizTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  questionContainer: {
    flex: 1,
    padding: 20,
  },
  questionText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    lineHeight: 26,
    marginBottom: 24,
  },
  optionsContainer: {
    marginBottom: 24,
  },
  option: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedOption: {
    borderColor: '#667eea',
    backgroundColor: '#EEF2FF',
  },
  correctOption: {
    borderColor: '#10B981',
    backgroundColor: '#F0FDF4',
  },
  wrongOption: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    flex: 1,
  },
  selectedOptionText: {
    color: '#1E40AF',
    fontFamily: 'Inter-Medium',
  },
  correctOptionText: {
    color: '#065F46',
    fontFamily: 'Inter-Medium',
  },
  wrongOptionText: {
    color: '#991B1B',
    fontFamily: 'Inter-Medium',
  },
  explanationContainer: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 12,
  },
  explanationTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  submitButton: {
    backgroundColor: '#667eea',
    margin: 20,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  resultTitle: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#111827',
    marginTop: 16,
  },
  resultScore: {
    fontSize: 48,
    fontFamily: 'Poppins-Bold',
    color: '#667eea',
    marginTop: 16,
  },
  resultPercentage: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 8,
  },
  finishButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 32,
  },
  finishButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  // New Card Styles
  completedQuizCard: {
    borderWidth: 2,
    borderColor: '#10B981',
  },
  cardGradient: {
    padding: 20,
    borderRadius: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  completionIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  progressScore: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
  },
  cardMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  startButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  buttonGradient: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
  },
  startButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  aiBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  aiText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#667eea',
  },
});