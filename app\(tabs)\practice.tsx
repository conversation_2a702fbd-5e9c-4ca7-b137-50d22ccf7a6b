import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Animated, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Target, Clock, Trophy, Star, Play, CircleCheck as CheckCircle, Circle as XCircle, Brain, Zap, Award } from 'lucide-react-native';
import { useQuizResults } from '@/hooks/useQuizResults';
import { useStudyProgress } from '@/hooks/useStudyProgress';

const { width } = Dimensions.get('window');

interface QuizQuestion {
  id: string;
  subject: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
}

interface Quiz {
  id: string;
  title: string;
  subject: string;
  duration: number;
  questions: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  color: string;
  icon: string;
}

export default function PracticeScreen() {
  const { results, saveQuizResult } = useQuizResults();
  const { updateProgress } = useStudyProgress();
  const [activeQuiz, setActiveQuiz] = useState<Quiz | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [score, setScore] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));
  const [progressAnim] = useState(new Animated.Value(0));

  const quizzes: Quiz[] = [
    {
      id: '1',
      title: 'English Foundation Quiz',
      subject: 'English',
      duration: 10,
      questions: 10,
      difficulty: 'Start Here',
      color: '#EF4444',
      icon: '🗣️',
    },
    {
      id: '2',
      title: 'Basic Physics Concepts',
      subject: 'Physics',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#3B82F6',
      icon: '⚛️',
    },
    {
      id: '3',
      title: 'Chemistry Basics',
      subject: 'Chemistry',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#10B981',
      icon: '🧪',
    },
    {
      id: '4',
      title: 'Math Fundamentals',
      subject: 'Mathematics',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#F59E0B',
      icon: '📐',
    },
    {
      id: '5',
      title: 'Biology Basics',
      subject: 'Biology',
      duration: 15,
      questions: 10,
      difficulty: 'Beginner',
      color: '#8B5CF6',
      icon: '🧬',
    },
  ];

  // Sample questions for demo
  const sampleQuestions: QuizQuestion[] = [
    {
      id: '1',
      subject: 'Physics',
      question: 'What is the SI unit of angular momentum?',
      options: ['kg⋅m²⋅s⁻¹', 'kg⋅m⋅s⁻¹', 'kg⋅m²⋅s⁻²', 'kg⋅s⁻¹'],
      correctAnswer: 0,
      explanation: 'Angular momentum = I × ω, where I is moment of inertia (kg⋅m²) and ω is angular velocity (s⁻¹).',
      difficulty: 'Medium',
    },
    {
      id: '2',
      subject: 'Physics',
      question: 'Which of the following is a vector quantity?',
      options: ['Angular speed', 'Angular displacement', 'Rotational kinetic energy', 'Moment of inertia'],
      correctAnswer: 1,
      explanation: 'Angular displacement has both magnitude and direction, making it a vector quantity.',
      difficulty: 'Easy',
    },
    {
      id: '3',
      subject: 'Physics',
      question: 'The moment of inertia of a solid sphere about its diameter is:',
      options: ['2/5 MR²', '2/3 MR²', '1/2 MR²', '1/3 MR²'],
      correctAnswer: 0,
      explanation: 'For a solid sphere of mass M and radius R, the moment of inertia about its diameter is 2/5 MR².',
      difficulty: 'Hard',
    },
  ];

  const calculateStats = () => {
    const totalCompleted = results.length;
    const averageScore = results.length > 0
      ? Math.round(results.reduce((sum, result) => sum + (result.score / result.total_questions * 100), 0) / results.length)
      : 0;
    const totalTime = results.reduce((sum, result) => sum + 15, 0); // Assuming 15 min average per quiz

    return {
      totalCompleted,
      averageScore,
      totalTime: `${Math.floor(totalTime / 60)}h ${totalTime % 60}m`,
    };
  };

  const stats = calculateStats();

  const startQuiz = (quiz: Quiz) => {
    setActiveQuiz(quiz);
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setScore(0);
    setShowResult(false);
    setQuizCompleted(false);
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const submitAnswer = () => {
    if (selectedAnswer === null) return;

    const currentQuestion = sampleQuestions[currentQuestionIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;

    if (isCorrect) {
      setScore(score + 1);
    }

    setShowResult(true);

    setTimeout(() => {
      if (currentQuestionIndex < sampleQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        setSelectedAnswer(null);
        setShowResult(false);
      } else {
        // Quiz completed
        setQuizCompleted(true);
      }
    }, 2000);
  };

  const finishQuiz = async () => {
    if (activeQuiz) {
      // Save quiz result
      await saveQuizResult(activeQuiz.subject, activeQuiz.title, score, sampleQuestions.length);

      // Update progress
      const percentage = Math.round((score / sampleQuestions.length) * 100);
      await updateProgress(activeQuiz.subject, activeQuiz.title, percentage, percentage >= 70);
    }

    setActiveQuiz(null);
    setQuizCompleted(false);
    Alert.alert(
      'Quiz Completed!',
      `You scored ${score}/${sampleQuestions.length} (${Math.round((score / sampleQuestions.length) * 100)}%)`,
      [{ text: 'OK' }]
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return '#10B981';
      case 'Medium': return '#F59E0B';
      case 'Hard': return '#EF4444';
      default: return '#6B7280';
    }
  };

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  React.useEffect(() => {
    if (activeQuiz) {
      const progress = ((currentQuestionIndex + 1) / sampleQuestions.length) * 100;
      Animated.timing(progressAnim, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [currentQuestionIndex, activeQuiz]);

  if (activeQuiz && !quizCompleted) {
    const currentQuestion = sampleQuestions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / sampleQuestions.length) * 100;

    return (
      <SafeAreaView style={styles.container}>
        {/* Quiz Header */}
        <LinearGradient
          colors={[activeQuiz.color, activeQuiz.color + 'CC']}
          style={styles.quizHeader}
        >
          <View style={styles.quizProgress}>
            <Text style={styles.questionCounter}>
              Question {currentQuestionIndex + 1} of {sampleQuestions.length}
            </Text>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBar}>
                <Animated.View
                  style={[
                    styles.progressFill,
                    {
                      width: progressAnim.interpolate({
                        inputRange: [0, 100],
                        outputRange: ['0%', '100%'],
                      }),
                      backgroundColor: '#FFFFFF'
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>{Math.round(progress)}%</Text>
            </View>
          </View>
          <Text style={styles.quizTitle}>{activeQuiz.title}</Text>
        </LinearGradient>

        {/* Question */}
        <ScrollView style={styles.questionContainer}>
          <Text style={styles.questionText}>{currentQuestion.question}</Text>

          {/* Options */}
          <View style={styles.optionsContainer}>
            {currentQuestion.options.map((option, index) => {
              let optionStyle = styles.option;
              let textStyle = styles.optionText;

              if (showResult) {
                if (index === currentQuestion.correctAnswer) {
                  optionStyle = [styles.option, styles.correctOption];
                  textStyle = [styles.optionText, styles.correctOptionText];
                } else if (index === selectedAnswer) {
                  optionStyle = [styles.option, styles.wrongOption];
                  textStyle = [styles.optionText, styles.wrongOptionText];
                }
              } else if (selectedAnswer === index) {
                optionStyle = [styles.option, styles.selectedOption];
                textStyle = [styles.optionText, styles.selectedOptionText];
              }

              return (
                <TouchableOpacity
                  key={index}
                  style={optionStyle}
                  onPress={() => handleAnswerSelect(index)}
                  disabled={showResult}
                >
                  <Text style={textStyle}>{option}</Text>
                  {showResult && index === currentQuestion.correctAnswer && (
                    <CheckCircle size={20} color="#10B981" />
                  )}
                  {showResult && index === selectedAnswer && index !== currentQuestion.correctAnswer && (
                    <XCircle size={20} color="#EF4444" />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Explanation */}
          {showResult && (
            <View style={styles.explanationContainer}>
              <Text style={styles.explanationTitle}>Explanation:</Text>
              <Text style={styles.explanationText}>{currentQuestion.explanation}</Text>
            </View>
          )}
        </ScrollView>

        {/* Submit Button */}
        {!showResult && (
          <TouchableOpacity
            style={[styles.submitButton, selectedAnswer === null && styles.submitButtonDisabled]}
            onPress={submitAnswer}
            disabled={selectedAnswer === null}
          >
            <Text style={styles.submitButtonText}>Submit Answer</Text>
          </TouchableOpacity>
        )}
      </SafeAreaView>
    );
  }

  if (quizCompleted) {
    const percentage = Math.round((score / sampleQuestions.length) * 100);
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.resultContainer}>
          <Trophy size={64} color="#F59E0B" />
          <Text style={styles.resultTitle}>Quiz Completed!</Text>
          <Text style={styles.resultScore}>{score}/{sampleQuestions.length}</Text>
          <Text style={styles.resultPercentage}>{percentage}%</Text>

          <TouchableOpacity style={styles.finishButton} onPress={finishQuiz}>
            <Text style={styles.finishButtonText}>Finish</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <Animated.View style={[styles.header, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <Brain size={28} color="#FFFFFF" />
            </View>
            <View>
              <Text style={styles.title}>Practice Tests</Text>
              <Text style={styles.subtitle}>Test your knowledge and track progress</Text>
            </View>
          </View>
        </Animated.View>
      </LinearGradient>

      {/* Stats */}
      <Animated.View style={[styles.statsContainer, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
        <LinearGradient
          colors={['#3B82F6', '#1E40AF']}
          style={styles.statCard}
        >
          <View style={styles.statIconContainer}>
            <Target size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.statValue}>{stats.totalCompleted}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </LinearGradient>

        <LinearGradient
          colors={['#F59E0B', '#D97706']}
          style={styles.statCard}
        >
          <View style={styles.statIconContainer}>
            <Star size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.statValue}>{stats.averageScore}%</Text>
          <Text style={styles.statLabel}>Avg Score</Text>
        </LinearGradient>

        <LinearGradient
          colors={['#10B981', '#059669']}
          style={styles.statCard}
        >
          <View style={styles.statIconContainer}>
            <Clock size={24} color="#FFFFFF" />
          </View>
          <Text style={styles.statValue}>{stats.totalTime}</Text>
          <Text style={styles.statLabel}>Time Spent</Text>
        </LinearGradient>
      </Animated.View>

      {/* Quizzes */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Animated.View style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}>
          <Text style={styles.sectionTitle}>Available Quizzes</Text>
        </Animated.View>

        {quizzes.map((quiz, index) => {
          const quizResult = results.find(r => r.quiz_title === quiz.title);
          const isCompleted = !!quizResult;
          const score = quizResult ? Math.round((quizResult.score / quizResult.total_questions) * 100) : 0;

          return (
            <Animated.View
              key={quiz.id}
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }}
            >
              <TouchableOpacity
                style={styles.quizCard}
                onPress={() => startQuiz(quiz)}
                activeOpacity={0.8}
              >
              <View style={styles.quizContent}>
                <View style={styles.quizLeft}>
                  <View style={[styles.quizIcon, { backgroundColor: `${quiz.color}20` }]}>
                    <Text style={styles.quizIconText}>{quiz.icon}</Text>
                  </View>
                  <View style={styles.quizInfo}>
                    <Text style={styles.quizCardTitle}>{quiz.title}</Text>
                    <Text style={styles.quizSubject}>{quiz.subject}</Text>
                    <View style={styles.quizMeta}>
                      <Text style={styles.quizMetaText}>{quiz.questions} questions</Text>
                      <Text style={styles.quizMetaText}>•</Text>
                      <Text style={styles.quizMetaText}>{quiz.duration} min</Text>
                    </View>
                  </View>
                </View>

                <View style={styles.quizRight}>
                  <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(quiz.difficulty) }]}>
                    <Text style={styles.difficultyText}>{quiz.difficulty}</Text>
                  </View>

                  {isCompleted ? (
                    <View style={styles.completedContainer}>
                      <CheckCircle size={16} color="#10B981" />
                      <Text style={styles.completedText}>{score}%</Text>
                    </View>
                  ) : (
                    <Play size={20} color="#6B7280" />
                  )}
                </View>
              </View>
            </TouchableOpacity>
            </Animated.View>
          );
        })}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingBottom: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    marginTop: -12,
    marginBottom: 32,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 20,
    borderRadius: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 22,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 20,
  },
  quizCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  quizContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quizLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  quizIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  quizIconText: {
    fontSize: 24,
  },
  quizInfo: {
    flex: 1,
  },
  quizCardTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  quizSubject: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  quizMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  quizMetaText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginRight: 8,
  },
  quizRight: {
    alignItems: 'flex-end',
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  difficultyText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  completedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completedText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#10B981',
    marginLeft: 4,
  },
  // Quiz screen styles
  quizHeader: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderBottomWidth: 3,
  },
  quizProgress: {
    marginBottom: 12,
  },
  questionCounter: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 8,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  quizTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
  },
  questionContainer: {
    flex: 1,
    padding: 20,
  },
  questionText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    lineHeight: 26,
    marginBottom: 24,
  },
  optionsContainer: {
    marginBottom: 24,
  },
  option: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedOption: {
    borderColor: '#667eea',
    backgroundColor: '#EEF2FF',
  },
  correctOption: {
    borderColor: '#10B981',
    backgroundColor: '#F0FDF4',
  },
  wrongOption: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    flex: 1,
  },
  selectedOptionText: {
    color: '#1E40AF',
    fontFamily: 'Inter-Medium',
  },
  correctOptionText: {
    color: '#065F46',
    fontFamily: 'Inter-Medium',
  },
  wrongOptionText: {
    color: '#991B1B',
    fontFamily: 'Inter-Medium',
  },
  explanationContainer: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 12,
  },
  explanationTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  submitButton: {
    backgroundColor: '#667eea',
    margin: 20,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  resultTitle: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#111827',
    marginTop: 16,
  },
  resultScore: {
    fontSize: 48,
    fontFamily: 'Poppins-Bold',
    color: '#667eea',
    marginTop: 16,
  },
  resultPercentage: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 8,
  },
  finishButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 32,
  },
  finishButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});