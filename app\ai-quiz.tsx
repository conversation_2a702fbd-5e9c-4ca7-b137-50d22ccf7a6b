import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Animated, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Clock, CheckCircle, XCircle, Lightbulb, Brain, Zap } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { AIQuizGenerator, AIQuizQuestion } from '@/lib/aiQuizGenerator';
import { useQuizResults } from '@/hooks/useQuizResults';
import { storage, QuizResult } from '@/lib/storage';

const { width, height } = Dimensions.get('window');

export default function AIQuizScreen() {
  const { subject, topic, difficulty } = useLocalSearchParams();
  const { saveQuizResult } = useQuizResults();

  const [questions, setQuestions] = useState<AIQuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showHint, setShowHint] = useState(false);

  // Animations
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [progressAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    generateQuiz();
    startTimer();
  }, []);

  useEffect(() => {
    if (questions.length > 0) {
      animateQuestionEntry();
    }
  }, [currentQuestionIndex, questions]);

  const generateQuiz = async () => {
    try {
      setLoading(true);
      const generatedQuestions = await AIQuizGenerator.generateQuiz({
        subject: subject as string,
        topic: topic as string,
        difficulty: difficulty as 'Beginner' | 'Intermediate' | 'Advanced',
        questionCount: 10,
        includeMarathiHints: true,
        studentLevel: 'average'
      });

      setQuestions(generatedQuestions);
    } catch (error) {
      console.error('Error generating quiz:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate quiz';

      if (errorMessage.includes('API_KEY_MISSING') || errorMessage.includes('API key')) {
        Alert.alert(
          'API Key Required',
          'To use AI-powered quizzes, please set up your Gemini API key:\n\n1. Get a free key from Google AI Studio\n2. Add it to your .env file\n3. Restart the app\n\nFor now, you can use the basic quiz mode.',
          [
            { text: 'Use Basic Quiz', onPress: () => router.back() },
            { text: 'OK', onPress: () => router.back() }
          ]
        );
      } else {
        // If we have questions from fallback, continue with those
        if (questions && questions.length > 0) {
          console.log('Using fallback questions');
          setLoading(false);
          return;
        }

        Alert.alert(
          'Quiz Generation Failed',
          'Unable to generate AI quiz. Using basic quiz mode instead.',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const startTimer = () => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          completeQuiz();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const animateQuestionEntry = () => {
    fadeAnim.setValue(0);
    slideAnim.setValue(50);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateProgress = () => {
    if (questions && questions.length > 0) {
      const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
      Animated.timing(progressAnim, {
        toValue: progress,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  };

  const handleAnswerSelect = (answerIndex: number) => {
    if (selectedAnswer !== null || !questions || questions.length === 0) return;

    setSelectedAnswer(answerIndex);
    setShowExplanation(true);

    // Animate selection
    Animated.sequence([
      Animated.timing(pulseAnim, {
        toValue: 1.1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();

    const currentQuestion = questions[currentQuestionIndex];
    if (currentQuestion && answerIndex === currentQuestion.correctAnswer) {
      setScore(score + 1);
    }

    // Auto advance after 3 seconds
    setTimeout(() => {
      nextQuestion();
    }, 3000);
  };

  const nextQuestion = () => {
    if (questions && questions.length > 0 && currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
      setShowHint(false);
      animateProgress();
    } else {
      completeQuiz();
    }
  };

  const completeQuiz = async () => {
    setQuizCompleted(true);

    if (!questions || questions.length === 0) {
      Alert.alert('Error', 'No questions available');
      router.back();
      return;
    }

    const percentage = Math.round((score / questions.length) * 100);
    const timeTaken = 300 - timeLeft;

    const quizResult: QuizResult = {
      id: Date.now().toString(),
      quiz_title: `AI Generated: ${topic}`,
      subject: subject as string,
      score,
      total_questions: questions.length,
      time_taken: timeTaken,
      completed_at: new Date().toISOString(),
      answers: questions.map((q, index) => ({
        question: q.question,
        selected: selectedAnswer || 0,
        correct: q.correctAnswer,
        isCorrect: selectedAnswer === q.correctAnswer,
      })),
    };

    await saveQuizResult(quizResult);

    Alert.alert(
      'Quiz Completed! 🎉',
      `Score: ${score}/${questions.length} (${percentage}%)\nTime: ${Math.floor(timeTaken / 60)}:${(timeTaken % 60).toString().padStart(2, '0')}`,
      [
        { text: 'Review Answers', onPress: () => {} },
        { text: 'Back to Practice', onPress: () => router.back() }
      ]
    );
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#667eea', '#764ba2']} style={styles.loadingContainer}>
          <Brain size={48} color="#FFFFFF" />
          <Text style={styles.loadingText}>AI is generating your personalized quiz...</Text>
          <Text style={styles.loadingSubtext}>This may take a few moments</Text>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (questions.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to generate quiz</Text>
          <TouchableOpacity style={styles.retryButton} onPress={generateQuiz}>
            <Text style={styles.retryText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const currentQuestion = questions && questions.length > 0 ? questions[currentQuestionIndex] : null;
  const progress = questions && questions.length > 0 ? ((currentQuestionIndex + 1) / questions.length) * 100 : 0;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient colors={['#667eea', '#764ba2']} style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>AI Quiz</Text>
          <Text style={styles.headerSubtitle}>{subject} • {topic}</Text>
        </View>

        <View style={styles.timerContainer}>
          <Clock size={20} color="#FFFFFF" />
          <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>
        </View>
      </LinearGradient>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                })
              }
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {currentQuestionIndex + 1} of {questions?.length || 0}
        </Text>
      </View>

      {/* Question */}
      {currentQuestion && (
        <Animated.View
          style={[
            styles.questionContainer,
            { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }
          ]}
        >
        <View style={styles.questionHeader}>
          <View style={styles.difficultyBadge}>
            <Text style={styles.difficultyText}>{currentQuestion.difficulty}</Text>
          </View>
          {currentQuestion.hintsInMarathi && (
            <TouchableOpacity
              style={styles.hintButton}
              onPress={() => setShowHint(!showHint)}
            >
              <Lightbulb size={20} color="#F59E0B" />
            </TouchableOpacity>
          )}
        </View>

        <Text style={styles.questionText}>{currentQuestion.question}</Text>

        {showHint && currentQuestion.hintsInMarathi && (
          <View style={styles.hintContainer}>
            <Text style={styles.hintText}>💡 {currentQuestion.hintsInMarathi}</Text>
          </View>
        )}

        {/* Options */}
        <View style={styles.optionsContainer}>
          {currentQuestion.options.map((option, index) => {
            let optionStyle = styles.optionButton;
            let textStyle = styles.optionText;
            let iconComponent = null;

            if (selectedAnswer !== null) {
              if (index === currentQuestion.correctAnswer) {
                optionStyle = [styles.optionButton, styles.correctOption];
                textStyle = [styles.optionText, styles.correctOptionText];
                iconComponent = <CheckCircle size={20} color="#059669" />;
              } else if (index === selectedAnswer && index !== currentQuestion.correctAnswer) {
                optionStyle = [styles.optionButton, styles.wrongOption];
                textStyle = [styles.optionText, styles.wrongOptionText];
                iconComponent = <XCircle size={20} color="#DC2626" />;
              }
            }

            return (
              <Animated.View key={index} style={{ transform: [{ scale: pulseAnim }] }}>
                <TouchableOpacity
                  style={optionStyle}
                  onPress={() => handleAnswerSelect(index)}
                  disabled={selectedAnswer !== null}
                >
                  <Text style={textStyle}>{option}</Text>
                  {iconComponent}
                </TouchableOpacity>
              </Animated.View>
            );
          })}
        </View>

        {/* Explanation */}
        {showExplanation && (
          <Animated.View
            style={[styles.explanationContainer, { opacity: fadeAnim }]}
          >
            <Text style={styles.explanationTitle}>Explanation:</Text>
            <Text style={styles.explanationText}>{currentQuestion.explanation}</Text>
          </Animated.View>
        )}
        </Animated.View>
      )}

      {/* Next Button */}
      {showExplanation && (
        <TouchableOpacity style={styles.nextButton} onPress={nextQuestion}>
          <LinearGradient colors={['#10B981', '#059669']} style={styles.nextButtonGradient}>
            <Text style={styles.nextButtonText}>
              {questions && currentQuestionIndex === questions.length - 1 ? 'Finish Quiz' : 'Next Question'}
            </Text>
            <Zap size={20} color="#FFFFFF" />
          </LinearGradient>
        </TouchableOpacity>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
  },
  loadingSubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#DC2626',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  retryText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  timerText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
  },
  questionContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  difficultyBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#D97706',
  },
  hintButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FEF3C7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionText: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    lineHeight: 28,
    marginBottom: 20,
  },
  hintContainer: {
    backgroundColor: '#FEF3C7',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  hintText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#92400E',
    lineHeight: 20,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  correctOption: {
    backgroundColor: '#F0FDF4',
    borderColor: '#059669',
  },
  wrongOption: {
    backgroundColor: '#FEF2F2',
    borderColor: '#DC2626',
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    flex: 1,
  },
  correctOptionText: {
    color: '#059669',
  },
  wrongOptionText: {
    color: '#DC2626',
  },
  explanationContainer: {
    backgroundColor: '#F0F9FF',
    padding: 20,
    borderRadius: 16,
    marginTop: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#0EA5E9',
  },
  explanationTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#0C4A6E',
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#0C4A6E',
    lineHeight: 20,
  },
  nextButton: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#10B981',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  nextButtonGradient: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});
