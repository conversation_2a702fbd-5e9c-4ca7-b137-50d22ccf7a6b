import React, { useEffect } from 'react';
import { View, StyleSheet, StatusBar, BackHandler } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import VideoPlayer from '@/components/VideoPlayer';
import * as ScreenOrientation from 'expo-screen-orientation';

export default function VideoPlayerScreen() {
  const { videoId, title } = useLocalSearchParams();

  const handleClose = () => {
    router.back();
  };

  useEffect(() => {
    // Allow landscape orientation for video
    ScreenOrientation.unlockAsync();

    // Handle back button on Android
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      handleClose();
      return true;
    });

    return () => {
      // Reset to portrait when leaving
      ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
      backHandler.remove();
    };
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      <VideoPlayer
        videoId={videoId as string}
        title={title as string}
        onClose={handleClose}
        showCloseButton={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
});
