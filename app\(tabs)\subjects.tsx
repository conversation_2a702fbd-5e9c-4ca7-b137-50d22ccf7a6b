import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Search, ChevronRight, BookOpen, Users, Clock, Star, TrendingUp, Target } from 'lucide-react-native';
import { router } from 'expo-router';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import ProgressCard from '@/components/ui/ProgressCard';

export default function SubjectsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const { progress, updateProgress } = useStudyProgress();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));

  const subjects = [
    {
      id: 'physics',
      name: 'Physics',
      color: '#3B82F6',
      bgColor: '#EEF2FF',
      icon: '⚡',
      totalTopics: 12,
      difficulty: 'Advanced',
      chapters: [
        'Rotational Dynamics',
        'Mechanical Properties of Fluids',
        'Kinetic Theory of Gases & Radiation',
        'Electrostatics',
        'Current Electricity',
        'Magnetic Effects of Current',
        'Electromagnetic Induction',
        'Electromagnetic Waves',
        'Ray Optics & Wave Optics',
        'Dual Nature of Matter',
        'Atoms and Nuclei',
        'Electronic Devices'
      ]
    },
    {
      id: 'chemistry',
      name: 'Chemistry',
      color: '#10B981',
      bgColor: '#F0FDF4',
      icon: '🧪',
      totalTopics: 16,
      difficulty: 'Intermediate',
      chapters: [
        'The Solid State',
        'Solutions and Colligative Properties',
        'Chemical Thermodynamics',
        'Electrochemistry',
        'Chemical Kinetics',
        'Surface Chemistry',
        'p-Block Elements',
        'd- and f-Block Elements',
        'Coordination Compounds',
        'Haloalkanes and Haloarenes',
        'Alcohols, Phenols, and Ethers',
        'Aldehydes, Ketones, and Carboxylic Acids',
        'Organic Compounds Containing Nitrogen',
        'Biomolecules',
        'Polymers',
        'Chemistry in Everyday Life'
      ]
    },
    {
      id: 'mathematics',
      name: 'Mathematics',
      color: '#F59E0B',
      bgColor: '#FEF3C7',
      icon: '📐',
      totalTopics: 18,
      difficulty: 'Advanced',
      chapters: [
        'Mathematical Logic',
        'Matrices',
        'Trigonometric Functions',
        'Pair of Straight Lines',
        'Circle & Conics',
        'Vectors',
        'Three-Dimensional Geometry',
        'Linear Programming Problems',
        'Continuity',
        'Differentiation',
        'Application of Derivatives',
        'Integration',
        'Application of Definite Integral',
        'Differential Equations',
        'Statistics',
        'Probability Distribution',
        'Bernoulli Trials',
        'Binomial Distribution'
      ]
    },
    {
      id: 'biology',
      name: 'Biology',
      color: '#8B5CF6',
      bgColor: '#F3F4F6',
      icon: '🧬',
      totalTopics: 14,
      difficulty: 'Intermediate',
      chapters: [
        'Reproduction in Lower and Higher Plants',
        'Plant Water Relations',
        'Photosynthesis and Respiration',
        'Genetics and Evolution',
        'Biotechnology',
        'Microbes in Human Welfare',
        'Organisms and Environment',
        'Human Reproduction',
        'Origin and Evolution of Life',
        'Chromosomal Basis of Inheritance',
        'Human Health & Diseases',
        'Human Physiology',
        'Genetic Engineering',
        'Animal Husbandry'
      ]
    },
    {
      id: 'english',
      name: 'English',
      color: '#06B6D4',
      bgColor: '#ECFEFF',
      icon: '📚',
      totalTopics: 8,
      difficulty: 'Beginner',
      chapters: [
        'Prose and Poetry',
        'Grammar Advanced',
        'Writing Skills',
        'Reading Comprehension',
        'Novel Study',
        'Creative Writing',
        'Communication Skills',
        'Literature Analysis'
      ]
    }
  ];

  const getSubjectProgress = (subjectId: string) => {
    const subjectProgress = progress.filter(p => p.subject.toLowerCase() === subjectId);
    const completedTopics = subjectProgress.filter(p => p.completed).length;
    const totalProgress = subjectProgress.length > 0
      ? Math.round(subjectProgress.reduce((sum, p) => sum + p.progress, 0) / subjectProgress.length)
      : 0;

    return { completedTopics, totalProgress };
  };

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    subject.chapters.some(chapter =>
      chapter.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const handleSubjectPress = (subject: any) => {
    router.push(`/subject-detail?id=${subject.id}&name=${subject.name}&color=${encodeURIComponent(subject.color)}`);
  };

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <Animated.View style={[styles.header, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <Text style={styles.title}>HSC Science Subjects</Text>
          <Text style={styles.subtitle}>Master all subjects for board exams</Text>
          <View style={styles.headerStats}>
            <View style={styles.statItem}>
              <BookOpen size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.statText}>{subjects.length} Subjects</Text>
            </View>
            <View style={styles.statItem}>
              <Target size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.statText}>
                {subjects.reduce((acc, subject) => acc + subject.totalTopics, 0)} Topics
              </Text>
            </View>
          </View>
        </Animated.View>
      </LinearGradient>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search subjects or topics..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#9CA3AF"
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {filteredSubjects.map((subject, index) => {
          const { completedTopics, totalProgress } = getSubjectProgress(subject.id);

          return (
            <Animated.View
              key={subject.id}
              style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}
            >
              <ProgressCard
                title={subject.name}
                progress={completedTopics}
                total={subject.totalTopics}
                color={subject.color}
                icon={<Text style={{ fontSize: 24 }}>{subject.icon}</Text>}
                delay={index * 100}
              />
              <TouchableOpacity
                style={styles.subjectCard}
                onPress={() => handleSubjectPress(subject)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[subject.color + '08', subject.color + '03']}
                  style={styles.cardGradient}
                >
                  <View style={styles.subjectHeader}>
                    <View style={styles.subjectInfo}>
                      <View style={[styles.subjectIconContainer, { backgroundColor: subject.color + '20' }]}>
                        <Text style={styles.subjectIcon}>{subject.icon}</Text>
                      </View>
                      <View style={styles.subjectDetails}>
                        <Text style={styles.subjectName}>{subject.name}</Text>
                        <Text style={styles.subjectStats}>
                          {completedTopics}/{subject.totalTopics} topics • {subject.difficulty}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.rightSection}>
                      <Text style={[styles.progressPercentage, { color: subject.color }]}>
                        {totalProgress}%
                      </Text>
                      <ChevronRight size={20} color="#9CA3AF" />
                    </View>
                  </View>

                  {/* Next Topic */}
                  <View style={styles.nextTopicContainer}>
                    <View style={[styles.nextTopicIcon, { backgroundColor: subject.color + '15' }]}>
                      <BookOpen size={14} color={subject.color} />
                    </View>
                    <Text style={styles.nextTopicText}>
                      Next: {subject.chapters[completedTopics] || 'All completed! 🎉'}
                    </Text>
                  </View>

                  {/* Quick Stats */}
                  <View style={styles.quickStats}>
                    <View style={styles.stat}>
                      <Clock size={14} color="#6B7280" />
                      <Text style={styles.statText}>2h avg</Text>
                    </View>
                    <View style={styles.stat}>
                      <Users size={14} color="#6B7280" />
                      <Text style={styles.statText}>12k students</Text>
                    </View>
                    <View style={styles.stat}>
                      <Star size={14} color="#F59E0B" />
                      <Text style={styles.statText}>4.8</Text>
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          );
        })}

        {/* Study Tips Card */}
        <Animated.View style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}>
          <LinearGradient
            colors={['#F59E0B', '#F97316']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.tipsCard}
          >
            <View style={styles.tipsHeader}>
              <Text style={styles.tipsIcon}>💡</Text>
              <Text style={styles.tipsTitle}>Study Tips</Text>
            </View>
            <Text style={styles.tipsText}>
              Focus on understanding concepts rather than memorization. Practice regularly and use AI tutor for doubts!
            </Text>
          </LinearGradient>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  subjectCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  subjectInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  subjectIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  subjectIcon: {
    fontSize: 28,
  },
  subjectDetails: {
    flex: 1,
  },
  subjectName: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
  },
  subjectStats: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
  nextTopicContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  nextTopicText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginLeft: 8,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  tipsCard: {
    backgroundColor: '#FEF3C7',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  tipsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#92400E',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#92400E',
    lineHeight: 20,
  },
});