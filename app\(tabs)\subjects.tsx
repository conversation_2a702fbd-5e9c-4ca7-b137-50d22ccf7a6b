import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Search, ChevronRight, BookOpen, Users, Clock, Star, TrendingUp, Target } from 'lucide-react-native';
import { router } from 'expo-router';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import ProgressCard from '@/components/ui/ProgressCard';

export default function SubjectsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const { progress, updateProgress } = useStudyProgress();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(30));

  const subjects = [
    {
      id: 'english',
      name: 'English (Foundation)',
      color: '#EF4444',
      bgColor: '#FEF2F2',
      icon: '🗣️',
      totalTopics: 12,
      difficulty: 'Start Here',
      description: 'Build English fluency from basics - Reading, Writing, Speaking',
      chapters: [
        'Basic Grammar & Sentence Formation',
        'Daily Conversation Practice (Marathi to English)',
        'Reading Simple Stories & Articles',
        'Essential Vocabulary (1000 words)',
        'Pronunciation & Speaking Practice',
        'Writing Simple Paragraphs',
        'Listening & Comprehension',
        'HSC Grammar Rules (Tenses, Voice, etc.)',
        'Essay & Letter Writing',
        'Yuvabharati Prose & Poetry',
        'Novel Study (To Sir, with Love)',
        'Advanced Communication Skills'
      ]
    },
    {
      id: 'physics',
      name: 'Physics',
      color: '#3B82F6',
      bgColor: '#EEF2FF',
      icon: '⚛️',
      totalTopics: 12,
      difficulty: 'Advanced',
      description: 'Maharashtra HSC Physics - Start with Class 11 basics',
      chapters: [
        'Rotational Dynamics',
        'Mechanical Properties of Fluids',
        'Kinetic Theory of Gases & Radiation',
        'Electrostatics',
        'Current Electricity',
        'Magnetic Effects of Current',
        'Electromagnetic Induction',
        'Electromagnetic Waves',
        'Ray Optics & Wave Optics',
        'Dual Nature of Matter',
        'Atoms and Nuclei',
        'Electronic Devices'
      ]
    },
    {
      id: 'chemistry',
      name: 'Chemistry',
      color: '#10B981',
      bgColor: '#F0FDF4',
      icon: '🧪',
      totalTopics: 16,
      difficulty: 'Intermediate',
      description: 'Physical, Organic & Inorganic Chemistry with practical focus',
      chapters: [
        'The Solid State',
        'Solutions and Colligative Properties',
        'Chemical Thermodynamics',
        'Electrochemistry',
        'Chemical Kinetics',
        'Surface Chemistry',
        'p-Block Elements (Groups 15,16,17,18)',
        'd- and f-Block Elements (Transition)',
        'Coordination Compounds',
        'Haloalkanes and Haloarenes',
        'Alcohols, Phenols, and Ethers',
        'Aldehydes, Ketones, and Carboxylic Acids',
        'Organic Compounds Containing Nitrogen',
        'Biomolecules (Carbohydrates, Proteins)',
        'Polymers',
        'Chemistry in Everyday Life'
      ]
    },
    {
      id: 'mathematics',
      name: 'Mathematics',
      color: '#F59E0B',
      bgColor: '#FEF3C7',
      icon: '📐',
      totalTopics: 17,
      difficulty: 'Advanced',
      description: 'Paper I & II - Algebra, Calculus, Statistics & Probability',
      chapters: [
        'Mathematical Logic',
        'Matrices',
        'Trigonometric Functions',
        'Pair of Straight Lines',
        'Circle & Conics',
        'Vectors',
        'Three-Dimensional Geometry (Lines & Planes)',
        'Linear Programming Problems',
        'Continuity',
        'Differentiation',
        'Application of Derivatives',
        'Integration',
        'Application of Definite Integral',
        'Differential Equations',
        'Statistics',
        'Probability Distribution',
        'Bernoulli Trials and Binomial Distribution'
      ]
    },
    {
      id: 'biology',
      name: 'Biology',
      color: '#8B5CF6',
      bgColor: '#F3E8FF',
      icon: '🧬',
      totalTopics: 14,
      difficulty: 'Intermediate',
      description: 'Botany & Zoology - Life processes, genetics, ecology',
      chapters: [
        'Reproduction in Lower and Higher Plants',
        'Plant Water Relation and Mineral Nutrition',
        'Photosynthesis and Respiration in Plants',
        'Genetics and Evolution (Heredity & Variation)',
        'Biotechnology (Principles & Applications)',
        'Microbes in Human Welfare',
        'Organisms and Environment I (Ecology)',
        'Organisms and Environment II (Biodiversity)',
        'Human Reproduction & Reproductive Health',
        'Origin and Evolution of Life',
        'Chromosomal Basis of Inheritance',
        'Human Health & Diseases',
        'Circulation, Excretion and Osmoregulation',
        'Control and Co-ordination (Human Physiology)'
      ]
    }
  ];

  const getSubjectProgress = (subjectId: string) => {
    const subjectProgress = progress.filter(p => p.subject.toLowerCase() === subjectId);
    const completedTopics = subjectProgress.filter(p => p.completed).length;
    const totalProgress = subjectProgress.length > 0
      ? Math.round(subjectProgress.reduce((sum, p) => sum + p.progress, 0) / subjectProgress.length)
      : 0;

    return { completedTopics, totalProgress };
  };

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    subject.chapters.some(chapter =>
      chapter.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const handleSubjectPress = (subject: any) => {
    router.push(`/subject-detail?id=${subject.id}&name=${subject.name}&color=${encodeURIComponent(subject.color)}`);
  };

  const getSubjectProgressLocal = (subjectId: string) => {
    const subjectProgress = progress.filter(p => p.subject.toLowerCase() === subjectId.toLowerCase());
    const completedTopics = subjectProgress.filter(p => p.completed).length;
    const totalProgress = subjectProgress.length > 0 ? Math.round((completedTopics / subjectProgress.length) * 100) : 0;

    return {
      completedTopics,
      totalProgress,
    };
  };

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <Animated.View style={[styles.header, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <Text style={styles.title}>HSC Science Subjects</Text>
          <Text style={styles.subtitle}>Master all subjects for board exams</Text>
          <View style={styles.headerStats}>
            <View style={styles.statItem}>
              <BookOpen size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.statText}>{subjects.length} Subjects</Text>
            </View>
            <View style={styles.statItem}>
              <Target size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.statText}>
                {subjects.reduce((acc, subject) => acc + subject.totalTopics, 0)} Topics
              </Text>
            </View>
          </View>
        </Animated.View>
      </LinearGradient>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search subjects or topics..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#9CA3AF"
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {filteredSubjects.map((subject, index) => {
          const { completedTopics, totalProgress } = getSubjectProgressLocal(subject.id);

          return (
            <Animated.View
              key={subject.id}
              style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}
            >
              <ProgressCard
                title={subject.name}
                progress={completedTopics}
                total={subject.totalTopics}
                color={subject.color}
                icon={<Text style={{ fontSize: 24 }}>{subject.icon}</Text>}
                delay={index * 100}
              />
              <TouchableOpacity
                style={styles.subjectCard}
                onPress={() => handleSubjectPress(subject)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[subject.color + '08', subject.color + '03']}
                  style={styles.cardGradient}
                >
                  <View style={styles.subjectHeader}>
                    <View style={styles.subjectInfo}>
                      <View style={[styles.subjectIconContainer, { backgroundColor: subject.color + '20' }]}>
                        <Text style={styles.subjectIcon}>{subject.icon}</Text>
                      </View>
                      <View style={styles.subjectDetails}>
                        <Text style={styles.subjectName}>{subject.name}</Text>
                        <Text style={styles.subjectStats}>
                          {completedTopics}/{subject.totalTopics} topics • {subject.difficulty}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.rightSection}>
                      <Text style={[styles.progressPercentage, { color: subject.color }]}>
                        {totalProgress}%
                      </Text>
                      <ChevronRight size={20} color="#9CA3AF" />
                    </View>
                  </View>

                  {/* Next Topic */}
                  <View style={styles.nextTopicContainer}>
                    <View style={[styles.nextTopicIcon, { backgroundColor: subject.color + '15' }]}>
                      <BookOpen size={14} color={subject.color} />
                    </View>
                    <Text style={styles.nextTopicText}>
                      Next: {subject.chapters[completedTopics] || 'All completed! 🎉'}
                    </Text>
                  </View>

                  {/* Quick Stats */}
                  <View style={styles.quickStats}>
                    <View style={styles.stat}>
                      <Clock size={14} color="#6B7280" />
                      <Text style={styles.statText}>2h avg</Text>
                    </View>
                    <View style={styles.stat}>
                      <Users size={14} color="#6B7280" />
                      <Text style={styles.statText}>12k students</Text>
                    </View>
                    <View style={styles.stat}>
                      <Star size={14} color="#F59E0B" />
                      <Text style={styles.statText}>4.8</Text>
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>
          );
        })}

        {/* Study Tips Card */}
        <Animated.View style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}>
          <LinearGradient
            colors={['#F59E0B', '#F97316']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.tipsCard}
          >
            <View style={styles.tipsHeader}>
              <Text style={styles.tipsIcon}>💡</Text>
              <Text style={styles.tipsTitle}>Study Tips</Text>
            </View>
            <Text style={styles.tipsText}>
              Focus on understanding concepts rather than memorization. Practice regularly and use AI tutor for doubts!
            </Text>
          </LinearGradient>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingBottom: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 20,
  },
  headerStats: {
    flexDirection: 'row',
    gap: 24,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginTop: -12,
    marginBottom: 24,
    borderRadius: 20,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
    zIndex: 10,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 18,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subjectCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 28,
    marginBottom: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
  },
  cardGradient: {
    padding: 24,
  },
  subjectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  subjectInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  subjectIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  subjectIcon: {
    fontSize: 32,
  },
  subjectDetails: {
    flex: 1,
  },
  subjectName: {
    fontSize: 22,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  rightSection: {
    alignItems: 'flex-end',
    gap: 8,
  },
  progressPercentage: {
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
  },
  subjectStats: {
    fontSize: 15,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  nextTopicContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding: 16,
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#F1F5F9',
  },
  nextTopicIcon: {
    width: 32,
    height: 32,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  nextTopicText: {
    fontSize: 15,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    flex: 1,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 6,
  },
  statText: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  tipsCard: {
    borderRadius: 24,
    padding: 24,
    marginTop: 8,
    marginBottom: 32,
    shadowColor: '#F59E0B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  tipsTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#FFFFFF',
  },
  tipsText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 22,
  },
});