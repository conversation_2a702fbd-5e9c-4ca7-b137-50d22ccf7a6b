import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, RefreshControl, Animated, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Target, TrendingUp, Clock, Play, Award, Flame, Star, Zap, Brain } from 'lucide-react-native';
import { router } from 'expo-router';
import { useAuth } from '@/hooks/useAuth';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import { useQuizResults } from '@/hooks/useQuizResults';
import { supabase } from '@/lib/supabase';

const { width } = Dimensions.get('window');

interface StudyStats {
  totalStudyTime: string;
  completedTopics: number;
  currentStreak: number;
  averageScore: number;
}

export default function HomeScreen() {
  const { user } = useAuth();
  const { progress } = useStudyProgress();
  const { results } = useQuizResults();
  const [stats, setStats] = useState<StudyStats>({
    totalStudyTime: '0h 0m',
    completedTopics: 0,
    currentStreak: 0,
    averageScore: 0,
  });
  const [refreshing, setRefreshing] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  useEffect(() => {
    calculateStats();
    // Animate components on mount
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [progress, results]);

  const calculateStats = async () => {
    if (!user) return;

    try {
      // Get study sessions for total time
      const { data: sessions } = await supabase
        .from('study_sessions')
        .select('duration')
        .eq('user_id', user.id);

      const totalMinutes = sessions?.reduce((sum, session) => sum + session.duration, 0) || 0;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // Calculate completed topics
      const completedTopics = progress.filter(p => p.completed).length;

      // Calculate average score
      const averageScore = results.length > 0
        ? Math.round(results.reduce((sum, result) => sum + (result.score / result.total_questions * 100), 0) / results.length)
        : 0;

      // Calculate streak (simplified - consecutive days with activity)
      const currentStreak = 7; // Placeholder - would need more complex logic

      setStats({
        totalStudyTime: `${hours}h ${minutes}m`,
        completedTopics,
        currentStreak,
        averageScore,
      });
    } catch (error) {
      console.error('Error calculating stats:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await calculateStats();
    setRefreshing(false);
  };

  const upcomingTopics = [
    { subject: 'English Foundation', topic: 'Basic Grammar & Vocabulary', time: 'Start Now', color: '#EF4444', icon: '🗣️' },
    { subject: 'Physics', topic: 'Rotational Dynamics (Class 11 Review)', time: 'After English', color: '#3B82F6', icon: '⚛️' },
    { subject: 'Chemistry', topic: 'The Solid State (Basics)', time: 'After Physics', color: '#10B981', icon: '🧪' },
  ];

  const recentAchievements = [
    { title: 'Welcome!', description: 'Start your HSC journey today', icon: '🎯', color: '#3B82F6' },
    { title: 'English First', description: 'Build strong foundation in English', icon: '🗣️', color: '#EF4444' },
    { title: 'Step by Step', description: 'Learn concepts gradually', icon: '📚', color: '#10B981' },
  ];

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning! 🌅';
    if (hour < 17) return 'Good Afternoon! ☀️';
    return 'Good Evening! 🌙';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <Animated.View style={[styles.header, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <View>
            <Text style={styles.greeting}>{getGreeting()}</Text>
            <Text style={styles.subtitle}>Ready to ace your HSC Science?</Text>
          </View>
          <TouchableOpacity style={styles.profileButton} onPress={() => router.push('/profile')}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' }}
              style={styles.profileImage}
            />
            <View style={styles.onlineIndicator} />
          </TouchableOpacity>
        </Animated.View>

        {/* Today's Stats */}
        <Animated.View style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.statsCard}
          >
            <View style={styles.statsHeader}>
              <Text style={styles.statsTitle}>Today's Progress</Text>
              <View style={styles.sparkleContainer}>
                <Star size={16} color="#FFFFFF" fill="#FFFFFF" />
              </View>
            </View>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <View style={styles.statIconContainer}>
                  <Clock size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.statValue}>{stats.totalStudyTime}</Text>
                <Text style={styles.statLabel}>Study Time</Text>
              </View>
              <View style={styles.statItem}>
                <View style={styles.statIconContainer}>
                  <Target size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.statValue}>{stats.completedTopics}</Text>
                <Text style={styles.statLabel}>Completed</Text>
              </View>
              <View style={styles.statItem}>
                <View style={styles.statIconContainer}>
                  <Flame size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.statValue}>{stats.currentStreak}</Text>
                <Text style={styles.statLabel}>Day Streak</Text>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View style={[styles.section, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          {/* Special English Learning Button */}
          <TouchableOpacity
            style={styles.englishSpecialButton}
            onPress={() => router.push('/english-learning')}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#EF4444', '#DC2626']}
              style={styles.englishGradient}
            >
              <View style={styles.englishButtonContent}>
                <Text style={styles.englishIcon}>🗣️</Text>
                <View style={styles.englishTextContainer}>
                  <Text style={styles.englishTitle}>Start with English</Text>
                  <Text style={styles.englishSubtitle}>मराठी ते इंग्रजी - Build foundation first</Text>
                </View>
                <Text style={styles.startText}>START</Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>

          <View style={styles.actionGrid}>
            <TouchableOpacity
              style={[styles.actionButton, styles.studyButton]}
              onPress={() => router.push('/subjects')}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#3B82F6', '#1E40AF']}
                style={styles.actionGradient}
              >
                <BookOpen size={28} color="#FFFFFF" />
                <Text style={[styles.actionText, { color: '#FFFFFF' }]}>All Subjects</Text>
              </LinearGradient>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.quizButton]}
              onPress={() => router.push('/practice')}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#10B981', '#059669']}
                style={styles.actionGradient}
              >
                <Target size={28} color="#FFFFFF" />
                <Text style={[styles.actionText, { color: '#FFFFFF' }]}>Practice</Text>
              </LinearGradient>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.aiButton]}
              onPress={() => router.push('/ai-tutor')}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#F59E0B', '#D97706']}
                style={styles.actionGradient}
              >
                <Brain size={28} color="#FFFFFF" />
                <Text style={[styles.actionText, { color: '#FFFFFF' }]}>AI Tutor</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Upcoming Study Schedule */}
        <Animated.View style={[styles.section, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Schedule</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          {upcomingTopics.map((item, index) => (
            <TouchableOpacity key={index} style={styles.scheduleItem} activeOpacity={0.7}>
              <View style={[styles.subjectIconBg, { backgroundColor: item.color + '20' }]}>
                <Text style={styles.subjectEmoji}>{item.icon}</Text>
              </View>
              <View style={styles.scheduleContent}>
                <Text style={styles.scheduleSubject}>{item.subject}</Text>
                <Text style={styles.scheduleTopic}>{item.topic}</Text>
              </View>
              <View style={styles.scheduleTimeContainer}>
                <Text style={styles.scheduleTime}>{item.time}</Text>
                <View style={[styles.statusDot, { backgroundColor: item.color }]} />
              </View>
            </TouchableOpacity>
          ))}
        </Animated.View>

        {/* Recent Achievements */}
        <Animated.View style={[styles.section, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Achievements</Text>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          {recentAchievements.map((achievement, index) => (
            <TouchableOpacity key={index} style={styles.achievementItem} activeOpacity={0.7}>
              <LinearGradient
                colors={[achievement.color + '20', achievement.color + '10']}
                style={styles.achievementIconContainer}
              >
                <Text style={styles.achievementIcon}>{achievement.icon}</Text>
              </LinearGradient>
              <View style={styles.achievementContent}>
                <Text style={styles.achievementTitle}>{achievement.title}</Text>
                <Text style={styles.achievementDescription}>{achievement.description}</Text>
              </View>
              <View style={styles.achievementBadge}>
                <Zap size={16} color={achievement.color} />
              </View>
            </TouchableOpacity>
          ))}
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  greeting: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  profileButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#10B981',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  statsCard: {
    marginHorizontal: 24,
    borderRadius: 24,
    padding: 28,
    marginBottom: 32,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  statsTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#FFFFFF',
  },
  sparkleContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 22,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  section: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
  },
  viewAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  viewAllText: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  actionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  actionGradient: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 20,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    marginTop: 12,
  },
  englishSpecialButton: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#EF4444',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  englishGradient: {
    padding: 20,
  },
  englishButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  englishIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  englishTextContainer: {
    flex: 1,
  },
  englishTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
  },
  englishSubtitle: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 2,
  },
  startText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  studyButton: {},
  quizButton: {},
  aiButton: {},
  scheduleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 20,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  subjectIconBg: {
    width: 48,
    height: 48,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  subjectEmoji: {
    fontSize: 24,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleSubject: {
    fontSize: 17,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
  },
  scheduleTopic: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  scheduleTimeContainer: {
    alignItems: 'flex-end',
  },
  scheduleTime: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 20,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  achievementIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  achievementIcon: {
    fontSize: 28,
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 17,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
  },
  achievementDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  achievementBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
});