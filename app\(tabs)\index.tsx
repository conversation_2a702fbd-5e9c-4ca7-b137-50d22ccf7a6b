import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, Target, TrendingUp, Clock, Play, Award, Flame } from 'lucide-react-native';
import { router } from 'expo-router';
import { useAuth } from '@/hooks/useAuth';
import { useStudyProgress } from '@/hooks/useStudyProgress';
import { useQuizResults } from '@/hooks/useQuizResults';
import { supabase } from '@/lib/supabase';

interface StudyStats {
  totalStudyTime: string;
  completedTopics: number;
  currentStreak: number;
  averageScore: number;
}

export default function HomeScreen() {
  const { user } = useAuth();
  const { progress } = useStudyProgress();
  const { results } = useQuizResults();
  const [stats, setStats] = useState<StudyStats>({
    totalStudyTime: '0h 0m',
    completedTopics: 0,
    currentStreak: 0,
    averageScore: 0,
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    calculateStats();
  }, [progress, results]);

  const calculateStats = async () => {
    if (!user) return;

    try {
      // Get study sessions for total time
      const { data: sessions } = await supabase
        .from('study_sessions')
        .select('duration')
        .eq('user_id', user.id);

      const totalMinutes = sessions?.reduce((sum, session) => sum + session.duration, 0) || 0;
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;

      // Calculate completed topics
      const completedTopics = progress.filter(p => p.completed).length;

      // Calculate average score
      const averageScore = results.length > 0 
        ? Math.round(results.reduce((sum, result) => sum + (result.score / result.total_questions * 100), 0) / results.length)
        : 0;

      // Calculate streak (simplified - consecutive days with activity)
      const currentStreak = 7; // Placeholder - would need more complex logic

      setStats({
        totalStudyTime: `${hours}h ${minutes}m`,
        completedTopics,
        currentStreak,
        averageScore,
      });
    } catch (error) {
      console.error('Error calculating stats:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await calculateStats();
    setRefreshing(false);
  };

  const upcomingTopics = [
    { subject: 'Physics', topic: 'Rotational Dynamics', time: '10:00 AM', color: '#3B82F6' },
    { subject: 'Chemistry', topic: 'p-Block Elements', time: '2:00 PM', color: '#10B981' },
    { subject: 'Mathematics', topic: 'Integration', time: '4:30 PM', color: '#F59E0B' },
  ];

  const recentAchievements = [
    { title: 'Physics Master', description: 'Completed 10 physics topics', icon: '🏆' },
    { title: 'Study Streak', description: `${stats.currentStreak} days in a row`, icon: '🔥' },
    { title: 'Quiz Champion', description: `${stats.averageScore}% average score`, icon: '⭐' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Good Morning! 👋</Text>
            <Text style={styles.subtitle}>Ready to ace your HSC Science?</Text>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <Image 
              source={{ uri: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop' }}
              style={styles.profileImage}
            />
          </TouchableOpacity>
        </View>

        {/* Today's Stats */}
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.statsCard}
        >
          <Text style={styles.statsTitle}>Today's Progress</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Clock size={20} color="#FFFFFF" />
              <Text style={styles.statValue}>{stats.totalStudyTime}</Text>
              <Text style={styles.statLabel}>Study Time</Text>
            </View>
            <View style={styles.statItem}>
              <Target size={20} color="#FFFFFF" />
              <Text style={styles.statValue}>{stats.completedTopics}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
            <View style={styles.statItem}>
              <Flame size={20} color="#FFFFFF" />
              <Text style={styles.statValue}>{stats.currentStreak}</Text>
              <Text style={styles.statLabel}>Day Streak</Text>
            </View>
          </View>
        </LinearGradient>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionGrid}>
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: '#EEF2FF' }]}
              onPress={() => router.push('/subjects')}
            >
              <BookOpen size={32} color="#3B82F6" />
              <Text style={[styles.actionText, { color: '#3B82F6' }]}>Study Now</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: '#F0FDF4' }]}
              onPress={() => router.push('/practice')}
            >
              <Target size={32} color="#10B981" />
              <Text style={[styles.actionText, { color: '#10B981' }]}>Take Quiz</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: '#FEF3C7' }]}
              onPress={() => router.push('/ai-tutor')}
            >
              <Text style={{ fontSize: 32 }}>🤖</Text>
              <Text style={[styles.actionText, { color: '#F59E0B' }]}>AI Help</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Upcoming Study Schedule */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Schedule</Text>
          {upcomingTopics.map((item, index) => (
            <TouchableOpacity key={index} style={styles.scheduleItem}>
              <View style={[styles.subjectDot, { backgroundColor: item.color }]} />
              <View style={styles.scheduleContent}>
                <Text style={styles.scheduleSubject}>{item.subject}</Text>
                <Text style={styles.scheduleTopic}>{item.topic}</Text>
              </View>
              <Text style={styles.scheduleTime}>{item.time}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Recent Achievements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Achievements</Text>
          {recentAchievements.map((achievement, index) => (
            <View key={index} style={styles.achievementItem}>
              <Text style={styles.achievementIcon}>{achievement.icon}</Text>
              <View style={styles.achievementContent}>
                <Text style={styles.achievementTitle}>{achievement.title}</Text>
                <Text style={styles.achievementDescription}>{achievement.description}</Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  greeting: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  profileButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    overflow: 'hidden',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  statsCard: {
    marginHorizontal: 20,
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
  },
  statsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#DBEAFE',
    marginTop: 4,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  actionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    marginHorizontal: 4,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginTop: 8,
  },
  scheduleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  subjectDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleSubject: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  scheduleTopic: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  scheduleTime: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  achievementIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  achievementDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
});