import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from './useAuth';

export interface QuizResult {
  id: string;
  subject: string;
  quiz_title: string;
  score: number;
  total_questions: number;
  completed_at: string;
}

export function useQuizResults() {
  const { user } = useAuth();
  const [results, setResults] = useState<QuizResult[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchResults();
    }
  }, [user]);

  const fetchResults = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('quiz_results')
        .select('*')
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false });

      if (error) throw error;
      setResults(data || []);
    } catch (error) {
      console.error('Error fetching quiz results:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveQuizResult = async (subject: string, quizTitle: string, score: number, totalQuestions: number) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('quiz_results')
        .insert({
          user_id: user.id,
          subject,
          quiz_title: quizTitle,
          score,
          total_questions: totalQuestions,
          completed_at: new Date().toISOString(),
        });

      if (error) throw error;
      await fetchResults();
    } catch (error) {
      console.error('Error saving quiz result:', error);
    }
  };

  return {
    results,
    loading,
    saveQuizResult,
    refetch: fetchResults,
  };
}