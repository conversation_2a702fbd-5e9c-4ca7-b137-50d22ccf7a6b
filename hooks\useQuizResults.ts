import { useState, useEffect } from 'react';
import { storage, QuizResult } from '@/lib/storage';

export function useQuizResults() {
  const [results, setResults] = useState<QuizResult[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchResults();
  }, []);

  const fetchResults = async () => {
    try {
      setLoading(true);
      const data = await storage.getQuizResults();
      setResults(data);
    } catch (error) {
      console.error('Error fetching quiz results:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveQuizResult = async (result: QuizResult) => {
    try {
      await storage.saveQuizResult(result);
      
      // Update local state
      setResults(prev => [result, ...prev]);
    } catch (error) {
      console.error('Error saving quiz result:', error);
    }
  };

  const getSubjectStats = (subject: string) => {
    const subjectResults = results.filter(r => r.subject.toLowerCase() === subject.toLowerCase());
    if (subjectResults.length === 0) {
      return { averageScore: 0, totalQuizzes: 0, totalTime: 0 };
    }

    const totalScore = subjectResults.reduce((sum, r) => sum + (r.score / r.total_questions) * 100, 0);
    const totalTime = subjectResults.reduce((sum, r) => sum + r.time_taken, 0);

    return {
      averageScore: Math.round(totalScore / subjectResults.length),
      totalQuizzes: subjectResults.length,
      totalTime: Math.round(totalTime / 60), // Convert to minutes
    };
  };

  return {
    results,
    loading,
    saveQuizResult,
    getSubjectStats,
    refetch: fetchResults,
  };
}
