import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Volume2, CheckCircle, Play, RotateCcw } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { storage } from '@/lib/storage';

interface LessonContent {
  title: string;
  description: string;
  content: Array<{
    type: 'text' | 'vocabulary' | 'exercise' | 'audio';
    data: any;
  }>;
}

export default function EnglishLessonScreen() {
  const { lesson, module } = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [userAnswers, setUserAnswers] = useState<string[]>([]);
  const [lessonCompleted, setLessonCompleted] = useState(false);

  const getLessonContent = (): LessonContent => {
    const lessonData: { [key: string]: LessonContent } = {
      'English Alphabet & Pronunciation': {
        title: 'English Alphabet & Pronunciation',
        description: 'Learn all 26 letters with correct pronunciation',
        content: [
          {
            type: 'text',
            data: {
              title: 'English Alphabet',
              text: 'The English alphabet has 26 letters. Let\'s learn them one by one with Marathi pronunciation guide.',
            }
          },
          {
            type: 'vocabulary',
            data: {
              title: 'Letters A-E',
              words: [
                { english: 'A', marathi: 'ए', pronunciation: 'ay' },
                { english: 'B', marathi: 'बी', pronunciation: 'bee' },
                { english: 'C', marathi: 'सी', pronunciation: 'see' },
                { english: 'D', marathi: 'डी', pronunciation: 'dee' },
                { english: 'E', marathi: 'ई', pronunciation: 'ee' },
              ]
            }
          },
          {
            type: 'exercise',
            data: {
              title: 'Practice: Choose the correct letter',
              question: 'Which letter comes after B?',
              options: ['A', 'C', 'D', 'E'],
              correct: 1,
              explanation: 'C comes after B in the alphabet sequence.'
            }
          }
        ]
      },
      'Basic Greetings (Hello, Good Morning)': {
        title: 'Basic Greetings',
        description: 'Learn common English greetings with Marathi translations',
        content: [
          {
            type: 'text',
            data: {
              title: 'Daily Greetings',
              text: 'Greetings are the first words we use when meeting someone. Let\'s learn the most common English greetings.',
            }
          },
          {
            type: 'vocabulary',
            data: {
              title: 'Common Greetings',
              words: [
                { english: 'Hello', marathi: 'नमस्कार', pronunciation: 'heh-loh' },
                { english: 'Good Morning', marathi: 'सुप्रभात', pronunciation: 'gud mor-ning' },
                { english: 'Good Afternoon', marathi: 'शुभ दुपार', pronunciation: 'gud af-ter-noon' },
                { english: 'Good Evening', marathi: 'शुभ संध्या', pronunciation: 'gud ee-vning' },
                { english: 'Good Night', marathi: 'शुभ रात्री', pronunciation: 'gud nahyt' },
              ]
            }
          },
          {
            type: 'exercise',
            data: {
              title: 'Practice: Choose the correct greeting',
              question: 'What do you say when you meet someone at 9 AM?',
              options: ['Good Evening', 'Good Morning', 'Good Night', 'Good Afternoon'],
              correct: 1,
              explanation: '9 AM is in the morning, so we say "Good Morning".'
            }
          }
        ]
      },
      'Basic Grammar & Sentence Formation': {
        title: 'Basic Grammar & Sentence Formation',
        description: 'Learn to make simple English sentences',
        content: [
          {
            type: 'text',
            data: {
              title: 'Simple Sentence Structure',
              text: 'A simple English sentence has three parts: Subject + Verb + Object\n\nExample: I (Subject) eat (Verb) food (Object)',
            }
          },
          {
            type: 'vocabulary',
            data: {
              title: 'Basic Sentence Examples',
              words: [
                { english: 'I eat food', marathi: 'मी अन्न खातो', pronunciation: 'ahy eet food' },
                { english: 'You go to school', marathi: 'तू शाळेत जातोस', pronunciation: 'yoo goh too skool' },
                { english: 'He reads books', marathi: 'तो पुस्तके वाचतो', pronunciation: 'hee reeds buks' },
                { english: 'She writes letters', marathi: 'ती पत्रे लिहिते', pronunciation: 'shee rahyts let-ers' },
              ]
            }
          },
          {
            type: 'exercise',
            data: {
              title: 'Practice: Complete the sentence',
              question: 'I ___ water. (मी पाणी पितो)',
              options: ['eat', 'drink', 'read', 'write'],
              correct: 1,
              explanation: 'We "drink" water. The correct sentence is "I drink water".'
            }
          }
        ]
      }
    };

    return lessonData[lesson as string] || lessonData['English Alphabet & Pronunciation'];
  };

  const lessonContent = getLessonContent();

  const handleNext = () => {
    if (currentStep < lessonContent.content.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeLesson();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleAnswer = (answer: string | number) => {
    const newAnswers = [...userAnswers];
    newAnswers[currentStep] = answer.toString();
    setUserAnswers(newAnswers);
  };

  const completeLesson = async () => {
    try {
      await storage.updateEnglishProgress(
        module as string,
        lesson as string,
        true,
        [],
        85 // Default score
      );
      
      setLessonCompleted(true);
      
      Alert.alert(
        'Lesson Completed! 🎉',
        'Great job! You have successfully completed this lesson. Keep practicing to improve your English skills.',
        [
          { text: 'Continue Learning', onPress: () => router.back() },
          { text: 'Practice Again', onPress: () => {
            setCurrentStep(0);
            setUserAnswers([]);
            setLessonCompleted(false);
          }}
        ]
      );
    } catch (error) {
      console.error('Error completing lesson:', error);
    }
  };

  const renderContent = () => {
    const content = lessonContent.content[currentStep];
    
    switch (content.type) {
      case 'text':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{content.data.title}</Text>
            <Text style={styles.contentText}>{content.data.text}</Text>
          </View>
        );
      
      case 'vocabulary':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{content.data.title}</Text>
            {content.data.words.map((word: any, index: number) => (
              <View key={index} style={styles.vocabularyItem}>
                <View style={styles.vocabularyText}>
                  <Text style={styles.englishWord}>{word.english}</Text>
                  <Text style={styles.marathiWord}>{word.marathi}</Text>
                  <Text style={styles.pronunciation}>/{word.pronunciation}/</Text>
                </View>
                <TouchableOpacity style={styles.audioButton}>
                  <Volume2 size={20} color="#667eea" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        );
      
      case 'exercise':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{content.data.title}</Text>
            <Text style={styles.questionText}>{content.data.question}</Text>
            <View style={styles.optionsContainer}>
              {content.data.options.map((option: string, index: number) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.optionButton,
                    userAnswers[currentStep] === index.toString() && styles.selectedOption
                  ]}
                  onPress={() => handleAnswer(index)}
                >
                  <Text style={[
                    styles.optionText,
                    userAnswers[currentStep] === index.toString() && styles.selectedOptionText
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            {userAnswers[currentStep] && (
              <View style={styles.explanationContainer}>
                <Text style={styles.explanationText}>{content.data.explanation}</Text>
              </View>
            )}
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#EF4444', '#DC2626']}
        style={styles.header}
      >
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.title}>{lessonContent.title}</Text>
          <Text style={styles.subtitle}>
            Step {currentStep + 1} of {lessonContent.content.length}
          </Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        {renderContent()}
      </ScrollView>

      <View style={styles.navigationContainer}>
        <TouchableOpacity 
          style={[styles.navButton, currentStep === 0 && styles.disabledButton]}
          onPress={handlePrevious}
          disabled={currentStep === 0}
        >
          <RotateCcw size={20} color={currentStep === 0 ? "#9CA3AF" : "#667eea"} />
          <Text style={[styles.navButtonText, currentStep === 0 && styles.disabledText]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.nextButton}
          onPress={handleNext}
        >
          <Text style={styles.nextButtonText}>
            {currentStep === lessonContent.content.length - 1 ? 'Complete' : 'Next'}
          </Text>
          <CheckCircle size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 24,
    paddingHorizontal: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  contentTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  contentText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  vocabularyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  vocabularyText: {
    flex: 1,
  },
  englishWord: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
  },
  marathiWord: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  pronunciation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    fontStyle: 'italic',
    marginTop: 2,
  },
  audioButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#EEF2FF',
    borderColor: '#667eea',
  },
  optionText: {
    fontSize: 15,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    textAlign: 'center',
  },
  selectedOptionText: {
    color: '#667eea',
  },
  explanationContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#059669',
    lineHeight: 20,
  },
  navigationContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  navButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    gap: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  navButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#667eea',
  },
  disabledText: {
    color: '#9CA3AF',
  },
  nextButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#667eea',
    gap: 8,
  },
  nextButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
});
