import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Volume2, CheckCircle, Play, RotateCcw, Brain, Sparkles } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { storage } from '@/lib/storage';
import { AIEnglishTutor, EnglishLessonContent } from '@/lib/aiEnglishTutor';

export default function EnglishLessonScreen() {
  const { lesson, module } = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [userAnswers, setUserAnswers] = useState<string[]>([]);
  const [lessonCompleted, setLessonCompleted] = useState(false);
  const [lessonContent, setLessonContent] = useState<EnglishLessonContent | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    generateLessonContent();
  }, []);

  const generateLessonContent = async () => {
    try {
      setLoading(true);

      // Determine student level based on lesson
      let studentLevel: 'beginner' | 'intermediate' | 'advanced' = 'beginner';
      if (lesson?.toString().includes('Advanced') || lesson?.toString().includes('Literature')) {
        studentLevel = 'advanced';
      } else if (lesson?.toString().includes('Grammar') || lesson?.toString().includes('Writing')) {
        studentLevel = 'intermediate';
      }

      const content = await AIEnglishTutor.generatePersonalizedLesson(
        lesson as string,
        studentLevel,
        [], // We can add weak areas from user progress later
        []  // We can add previous mistakes later
      );

      setLessonContent(content);
    } catch (error) {
      console.error('Error generating lesson:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate lesson';

      if (errorMessage.includes('API key')) {
        Alert.alert(
          'API Key Required',
          'To use AI-powered lessons, please set up your Gemini API key:\n\n1. Get a free key from Google AI Studio\n2. Add it to your .env file\n3. Restart the app',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        Alert.alert('Error', errorMessage);
        router.back();
      }
    } finally {
      setLoading(false);
    }
  };



  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient colors={['#EF4444', '#DC2626']} style={styles.loadingContainer}>
          <Brain size={48} color="#FFFFFF" />
          <Text style={styles.loadingText}>AI is creating your personalized lesson...</Text>
          <Text style={styles.loadingSubtext}>Tailored for Marathi speakers</Text>
          <ActivityIndicator size="large" color="#FFFFFF" style={{ marginTop: 20 }} />
        </LinearGradient>
      </SafeAreaView>
    );
  }

  if (!lessonContent) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load lesson</Text>
          <TouchableOpacity style={styles.retryButton} onPress={generateLessonContent}>
            <Text style={styles.retryText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const handleNext = () => {
    if (lessonContent && lessonContent.sections && currentStep < lessonContent.sections.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeLesson();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleAnswer = (answer: string | number) => {
    const newAnswers = [...userAnswers];
    newAnswers[currentStep] = answer.toString();
    setUserAnswers(newAnswers);
  };

  const completeLesson = async () => {
    try {
      await storage.updateEnglishProgress(
        module as string,
        lesson as string,
        true,
        [],
        85 // Default score
      );

      setLessonCompleted(true);

      Alert.alert(
        'Lesson Completed! 🎉',
        'Great job! You have successfully completed this lesson. Keep practicing to improve your English skills.',
        [
          { text: 'Continue Learning', onPress: () => router.back() },
          { text: 'Practice Again', onPress: () => {
            setCurrentStep(0);
            setUserAnswers([]);
            setLessonCompleted(false);
          }}
        ]
      );
    } catch (error) {
      console.error('Error completing lesson:', error);
    }
  };

  const renderContent = () => {
    if (!lessonContent || !lessonContent.sections || lessonContent.sections.length === 0) return null;

    const section = lessonContent.sections[currentStep];
    if (!section) return null;

    switch (section.type) {
      case 'introduction':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{section.title}</Text>
            <Text style={styles.contentText}>{section.content.text}</Text>
            {section.content.marathiText && (
              <View style={styles.marathiContainer}>
                <Text style={styles.marathiText}>{section.content.marathiText}</Text>
              </View>
            )}
            {section.content.objectives && (
              <View style={styles.objectivesContainer}>
                <Text style={styles.objectivesTitle}>Learning Objectives:</Text>
                {section.content.objectives.map((objective: string, index: number) => (
                  <Text key={index} style={styles.objectiveText}>• {objective}</Text>
                ))}
              </View>
            )}
          </View>
        );

      case 'vocabulary':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{section.title}</Text>
            {section.content.words?.map((word: any, index: number) => (
              <View key={index} style={styles.vocabularyItem}>
                <View style={styles.vocabularyText}>
                  <Text style={styles.englishWord}>{word.english}</Text>
                  <Text style={styles.marathiWord}>{word.marathi}</Text>
                  <Text style={styles.pronunciation}>/{word.pronunciation}/</Text>
                  <Text style={styles.exampleText}>{word.example}</Text>
                  <Text style={styles.marathiExample}>{word.marathiExample}</Text>
                </View>
                <TouchableOpacity style={styles.audioButton}>
                  <Volume2 size={20} color="#667eea" />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        );

      case 'grammar':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{section.title}</Text>
            {section.content.rules?.map((rule: any, index: number) => (
              <View key={index} style={styles.grammarRule}>
                <Text style={styles.ruleTitle}>{rule.rule}</Text>
                <Text style={styles.ruleExplanation}>{rule.explanation}</Text>
                <Text style={styles.marathiExplanation}>{rule.marathiExplanation}</Text>
                {rule.examples?.map((example: any, exIndex: number) => (
                  <View key={exIndex} style={styles.exampleContainer}>
                    <Text style={[styles.exampleText, example.correct ? styles.correctExample : styles.incorrectExample]}>
                      {example.english}
                    </Text>
                    <Text style={styles.marathiExample}>{example.marathi}</Text>
                  </View>
                ))}
              </View>
            ))}
          </View>
        );

      case 'conversation':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{section.title}</Text>
            <Text style={styles.scenarioText}>{section.content.scenario}</Text>
            <Text style={styles.marathiScenario}>{section.content.marathiScenario}</Text>
            {section.content.dialogue?.map((line: any, index: number) => (
              <View key={index} style={styles.dialogueLine}>
                <Text style={styles.speaker}>Speaker {line.speaker}:</Text>
                <Text style={styles.englishDialogue}>{line.english}</Text>
                <Text style={styles.marathiDialogue}>{line.marathi}</Text>
                <Text style={styles.pronunciation}>/{line.pronunciation}/</Text>
              </View>
            ))}
          </View>
        );

      case 'exercise':
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{section.title}</Text>
            {section.content.exercises?.map((exercise: any, index: number) => (
              <View key={index} style={styles.exerciseContainer}>
                <Text style={styles.questionText}>{exercise.question}</Text>
                {exercise.marathiQuestion && (
                  <Text style={styles.marathiQuestion}>{exercise.marathiQuestion}</Text>
                )}
                <View style={styles.optionsContainer}>
                  {exercise.options?.map((option: string, optIndex: number) => (
                    <TouchableOpacity
                      key={optIndex}
                      style={[
                        styles.optionButton,
                        userAnswers[currentStep] === optIndex.toString() && styles.selectedOption
                      ]}
                      onPress={() => handleAnswer(optIndex)}
                    >
                      <Text style={[
                        styles.optionText,
                        userAnswers[currentStep] === optIndex.toString() && styles.selectedOptionText
                      ]}>
                        {option}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                {userAnswers[currentStep] && (
                  <View style={styles.explanationContainer}>
                    <Text style={styles.explanationText}>{exercise.explanation}</Text>
                    <Text style={styles.marathiExplanation}>{exercise.marathiExplanation}</Text>
                  </View>
                )}
              </View>
            ))}
          </View>
        );

      default:
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.contentTitle}>{section.title}</Text>
            <Text style={styles.contentText}>Content type not yet supported</Text>
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#EF4444', '#DC2626']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.title}>{lessonContent?.title || 'English Lesson'}</Text>
          <Text style={styles.subtitle}>
            Step {currentStep + 1} of {lessonContent?.sections?.length || 1}
          </Text>
          <View style={styles.aiIndicator}>
            <Sparkles size={16} color="#FFFFFF" />
            <Text style={styles.aiText}>AI Personalized</Text>
          </View>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        {renderContent()}
      </ScrollView>

      <View style={styles.navigationContainer}>
        <TouchableOpacity
          style={[styles.navButton, currentStep === 0 && styles.disabledButton]}
          onPress={handlePrevious}
          disabled={currentStep === 0}
        >
          <RotateCcw size={20} color={currentStep === 0 ? "#9CA3AF" : "#667eea"} />
          <Text style={[styles.navButtonText, currentStep === 0 && styles.disabledText]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.nextButton}
          onPress={handleNext}
        >
          <Text style={styles.nextButtonText}>
            {currentStep === lessonContent.content.length - 1 ? 'Complete' : 'Next'}
          </Text>
          <CheckCircle size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 24,
    paddingHorizontal: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerContent: {
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  contentTitle: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  contentText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  vocabularyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  vocabularyText: {
    flex: 1,
  },
  englishWord: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
  },
  marathiWord: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  pronunciation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    fontStyle: 'italic',
    marginTop: 2,
  },
  audioButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  optionsContainer: {
    gap: 12,
  },
  optionButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#EEF2FF',
    borderColor: '#667eea',
  },
  optionText: {
    fontSize: 15,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    textAlign: 'center',
  },
  selectedOptionText: {
    color: '#667eea',
  },
  explanationContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#059669',
    lineHeight: 20,
  },
  navigationContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  navButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    gap: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  navButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#667eea',
  },
  disabledText: {
    color: '#9CA3AF',
  },
  nextButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: '#667eea',
    gap: 8,
  },
  nextButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
  },
  loadingSubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Poppins-SemiBold',
    color: '#DC2626',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  retryText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  aiIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
  },
  aiText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  marathiContainer: {
    backgroundColor: '#FEF3C7',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  marathiText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#92400E',
    lineHeight: 20,
  },
  objectivesContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
  },
  objectivesTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#0C4A6E',
    marginBottom: 8,
  },
  objectiveText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#0C4A6E',
    marginBottom: 4,
  },
  exampleText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    marginTop: 4,
  },
  marathiExample: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
    fontStyle: 'italic',
  },
  grammarRule: {
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  ruleTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  ruleExplanation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 20,
    marginBottom: 8,
  },
  marathiExplanation: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
    marginBottom: 12,
  },
  exampleContainer: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  correctExample: {
    color: '#059669',
  },
  incorrectExample: {
    color: '#DC2626',
  },
  scenarioText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  marathiScenario: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  dialogueLine: {
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  speaker: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#4F46E5',
    marginBottom: 4,
  },
  englishDialogue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  marathiDialogue: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  exerciseContainer: {
    marginBottom: 20,
  },
  marathiQuestion: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 12,
    fontStyle: 'italic',
  },
});
