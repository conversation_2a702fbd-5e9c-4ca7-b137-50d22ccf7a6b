import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const STORAGE_KEYS = {
  USER_PROGRESS: 'user_progress',
  QUIZ_RESULTS: 'quiz_results',
  ENGLISH_PROGRESS: 'english_progress',
  STUDY_STATS: 'study_stats',
  COMPLETED_LESSONS: 'completed_lessons',
  USER_PREFERENCES: 'user_preferences',
};

// Types
export interface UserProgress {
  subject: string;
  topic: string;
  completed: boolean;
  score?: number;
  timeSpent: number;
  lastStudied: string;
}

export interface QuizResult {
  id: string;
  quiz_title: string;
  subject: string;
  score: number;
  total_questions: number;
  time_taken: number;
  completed_at: string;
  answers: Array<{
    question: string;
    selected: number;
    correct: number;
    isCorrect: boolean;
  }>;
}

export interface EnglishProgress {
  module: string;
  lesson: string;
  completed: boolean;
  score?: number;
  vocabularyLearned: string[];
  lastPracticed: string;
}

export interface StudyStats {
  totalStudyTime: number; // in minutes
  completedTopics: number;
  currentStreak: number;
  averageScore: number;
  lastStudyDate: string;
  subjectStats: {
    [subject: string]: {
      timeSpent: number;
      topicsCompleted: number;
      averageScore: number;
    };
  };
}

// Storage functions
export const storage = {
  // Generic storage functions
  async setItem(key: string, value: any): Promise<void> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error saving to storage:', error);
    }
  },

  async getItem<T>(key: string): Promise<T | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Error reading from storage:', error);
      return null;
    }
  },

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from storage:', error);
    }
  },

  // User Progress
  async getUserProgress(): Promise<UserProgress[]> {
    return (await this.getItem<UserProgress[]>(STORAGE_KEYS.USER_PROGRESS)) || [];
  },

  async saveUserProgress(progress: UserProgress[]): Promise<void> {
    await this.setItem(STORAGE_KEYS.USER_PROGRESS, progress);
  },

  async updateTopicProgress(subject: string, topic: string, completed: boolean, score?: number, timeSpent: number = 0): Promise<void> {
    const progress = await this.getUserProgress();
    const existingIndex = progress.findIndex(p => p.subject === subject && p.topic === topic);
    
    const updatedProgress: UserProgress = {
      subject,
      topic,
      completed,
      score,
      timeSpent,
      lastStudied: new Date().toISOString(),
    };

    if (existingIndex >= 0) {
      progress[existingIndex] = updatedProgress;
    } else {
      progress.push(updatedProgress);
    }

    await this.saveUserProgress(progress);
    await this.updateStudyStats(subject, timeSpent, completed, score);
  },

  // Quiz Results
  async getQuizResults(): Promise<QuizResult[]> {
    return (await this.getItem<QuizResult[]>(STORAGE_KEYS.QUIZ_RESULTS)) || [];
  },

  async saveQuizResult(result: QuizResult): Promise<void> {
    const results = await this.getQuizResults();
    results.push(result);
    await this.setItem(STORAGE_KEYS.QUIZ_RESULTS, results);
    
    // Update study stats
    await this.updateStudyStats(result.subject, result.time_taken, true, (result.score / result.total_questions) * 100);
  },

  // English Progress
  async getEnglishProgress(): Promise<EnglishProgress[]> {
    return (await this.getItem<EnglishProgress[]>(STORAGE_KEYS.ENGLISH_PROGRESS)) || [];
  },

  async updateEnglishProgress(module: string, lesson: string, completed: boolean, vocabularyLearned: string[] = [], score?: number): Promise<void> {
    const progress = await this.getEnglishProgress();
    const existingIndex = progress.findIndex(p => p.module === module && p.lesson === lesson);
    
    const updatedProgress: EnglishProgress = {
      module,
      lesson,
      completed,
      score,
      vocabularyLearned,
      lastPracticed: new Date().toISOString(),
    };

    if (existingIndex >= 0) {
      progress[existingIndex] = updatedProgress;
    } else {
      progress.push(updatedProgress);
    }

    await this.setItem(STORAGE_KEYS.ENGLISH_PROGRESS, progress);
  },

  // Study Stats
  async getStudyStats(): Promise<StudyStats> {
    const defaultStats: StudyStats = {
      totalStudyTime: 0,
      completedTopics: 0,
      currentStreak: 0,
      averageScore: 0,
      lastStudyDate: '',
      subjectStats: {},
    };
    
    return (await this.getItem<StudyStats>(STORAGE_KEYS.STUDY_STATS)) || defaultStats;
  },

  async updateStudyStats(subject: string, timeSpent: number, topicCompleted: boolean, score?: number): Promise<void> {
    const stats = await this.getStudyStats();
    const today = new Date().toDateString();
    
    // Update total stats
    stats.totalStudyTime += timeSpent;
    if (topicCompleted) {
      stats.completedTopics += 1;
    }
    
    // Update streak
    if (stats.lastStudyDate === today) {
      // Same day, don't change streak
    } else if (stats.lastStudyDate === new Date(Date.now() - 86400000).toDateString()) {
      // Yesterday, increment streak
      stats.currentStreak += 1;
    } else if (stats.lastStudyDate !== today) {
      // Different day, reset streak
      stats.currentStreak = 1;
    }
    
    stats.lastStudyDate = today;
    
    // Update subject stats
    if (!stats.subjectStats[subject]) {
      stats.subjectStats[subject] = {
        timeSpent: 0,
        topicsCompleted: 0,
        averageScore: 0,
      };
    }
    
    stats.subjectStats[subject].timeSpent += timeSpent;
    if (topicCompleted) {
      stats.subjectStats[subject].topicsCompleted += 1;
    }
    
    if (score !== undefined) {
      const currentAvg = stats.subjectStats[subject].averageScore;
      const completedCount = stats.subjectStats[subject].topicsCompleted;
      stats.subjectStats[subject].averageScore = ((currentAvg * (completedCount - 1)) + score) / completedCount;
      
      // Update overall average
      const allResults = await this.getQuizResults();
      if (allResults.length > 0) {
        const totalScore = allResults.reduce((sum, result) => sum + (result.score / result.total_questions) * 100, 0);
        stats.averageScore = totalScore / allResults.length;
      }
    }
    
    await this.setItem(STORAGE_KEYS.STUDY_STATS, stats);
  },

  // Completed Lessons
  async getCompletedLessons(): Promise<string[]> {
    return (await this.getItem<string[]>(STORAGE_KEYS.COMPLETED_LESSONS)) || [];
  },

  async markLessonCompleted(lessonId: string): Promise<void> {
    const completed = await this.getCompletedLessons();
    if (!completed.includes(lessonId)) {
      completed.push(lessonId);
      await this.setItem(STORAGE_KEYS.COMPLETED_LESSONS, completed);
    }
  },

  // Clear all data (for testing)
  async clearAllData(): Promise<void> {
    await AsyncStorage.clear();
  },
};
