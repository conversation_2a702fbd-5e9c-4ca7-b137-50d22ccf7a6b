import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Alert, KeyboardAvoidingView, Platform, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Send, Bot, User, Sparkles, BookOpen, Brain, Zap } from 'lucide-react-native';
import Markdown from 'react-native-markdown-display';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function AITutorScreen() {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "नमस्कार! 🙏 Hello! I'm your AI tutor for Maharashtra HSC Science.\n\n**मी तुम्हाला मदत करू शकतो:** (I can help you with:)\n\n• **English Learning** - Basic to advanced, Marathi explanations\n• **Physics** - Start from Class 11 basics, build concepts\n• **Chemistry** - Step-by-step with practical examples\n• **Mathematics** - From fundamentals to advanced calculus\n• **Biology** - Life processes, genetics, human body\n\n**Ask me in English or Marathi!** मराठीत किंवा इंग्रजीत विचारा!\n\nWhat would you like to learn today? आज काय शिकायचे आहे?",
      isUser: false,
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const [fadeAnim] = useState(new Animated.Value(0));

  const quickQuestions = [
    { text: "मला इंग्रजी शिकायची आहे (I want to learn English)", icon: "🗣️", color: "#EF4444" },
    { text: "Basic physics concepts समजावून सांगा", icon: "⚛️", color: "#3B82F6" },
    { text: "Chemistry मध्ये मला मदत हवी", icon: "🧪", color: "#10B981" },
    { text: "Math problems कसे सोडवायचे?", icon: "📐", color: "#F59E0B" },
    { text: "Biology basics समजावा", icon: "🧬", color: "#8B5CF6" },
    { text: "HSC exam preparation tips", icon: "📝", color: "#06B6D4" }
  ];

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
    // Animate on mount
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, [messages]);

  const callGeminiAPI = async (prompt: string): Promise<string> => {
    try {
      const API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
      if (!API_KEY) {
        throw new Error('Gemini API key not found');
      }

      const enhancedPrompt = `You are an expert HSC Science tutor specializing in Maharashtra Board Class 12 Science subjects (Physics, Chemistry, Mathematics, Biology, and English).

      Please provide a comprehensive, educational response to this student question: "${prompt}"

      Guidelines:
      - Give clear, step-by-step explanations
      - Use simple language appropriate for Class 12 students
      - Include relevant examples and formulas where applicable
      - Focus on conceptual understanding
      - If it's a problem, show the solution process
      - Keep responses concise but thorough (2-3 paragraphs max)
      - Encourage further learning

      Question: ${prompt}`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: enhancedPrompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Invalid response format from Gemini API');
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      return "I apologize, but I'm having trouble connecting to my knowledge base right now. Please try again in a moment, or rephrase your question. In the meantime, you can refer to your textbooks or ask your teacher for help with this topic.";
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      const aiResponse = await callGeminiAPI(inputText.trim());

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);

      // Save study session
      if (user) {
        await supabase.from('study_sessions').insert({
          user_id: user.id,
          subject: 'AI Tutoring',
          duration: 5, // Approximate 5 minutes per AI interaction
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to get response from AI tutor. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickQuestion = (question: string) => {
    setInputText(question);
  };

  const markdownStyles = {
    body: {
      fontSize: 15,
      lineHeight: 22,
      fontFamily: 'Inter-Regular',
      color: '#374151',
    },
    heading1: {
      fontSize: 18,
      fontFamily: 'Poppins-SemiBold',
      color: '#1F2937',
      marginBottom: 8,
    },
    heading2: {
      fontSize: 16,
      fontFamily: 'Poppins-SemiBold',
      color: '#1F2937',
      marginBottom: 6,
    },
    strong: {
      fontFamily: 'Inter-SemiBold',
      color: '#1F2937',
    },
    em: {
      fontFamily: 'Inter-Regular',
      fontStyle: 'italic',
    },
    list_item: {
      fontSize: 15,
      lineHeight: 22,
      fontFamily: 'Inter-Regular',
      color: '#374151',
      marginBottom: 4,
    },
    code_inline: {
      backgroundColor: '#F3F4F6',
      color: '#1F2937',
      fontFamily: 'Inter-Medium',
      fontSize: 14,
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 4,
    },
    code_block: {
      backgroundColor: '#F9FAFB',
      color: '#1F2937',
      fontFamily: 'Inter-Medium',
      fontSize: 14,
      padding: 12,
      borderRadius: 8,
      marginVertical: 8,
    },
  };

  const renderMessage = (message: Message) => (
    <Animated.View
      key={message.id}
      style={[
        styles.messageContainer,
        message.isUser ? styles.userMessage : styles.aiMessage,
        { opacity: fadeAnim }
      ]}
    >
      <View style={styles.messageHeader}>
        <View style={[
          styles.avatarContainer,
          message.isUser ? styles.userAvatar : styles.aiAvatar
        ]}>
          {message.isUser ? (
            <User size={14} color="#FFFFFF" />
          ) : (
            <Brain size={14} color="#FFFFFF" />
          )}
        </View>
        <Text style={styles.messageSender}>
          {message.isUser ? 'You' : 'AI Tutor'}
        </Text>
        {!message.isUser && (
          <View style={styles.aiIndicator}>
            <Zap size={12} color="#10B981" />
          </View>
        )}
      </View>

      {message.isUser ? (
        <Text style={[
          styles.messageText,
          styles.userMessageText
        ]}>
          {message.text}
        </Text>
      ) : (
        <View style={styles.aiMessageContent}>
          <Markdown style={markdownStyles}>
            {message.text}
          </Markdown>
        </View>
      )}

      <Text style={styles.messageTime}>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </Text>
    </Animated.View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <View style={styles.headerIconContainer}>
              <Brain size={24} color="#FFFFFF" />
            </View>
            <View>
              <Text style={styles.title}>AI Tutor</Text>
              <Text style={styles.subtitle}>Your personal HSC Science assistant</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.headerButton}>
            <Sparkles size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.chatContainer}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
        >
          {messages.map(renderMessage)}

          {isLoading && (
            <Animated.View style={[styles.messageContainer, styles.aiMessage, { opacity: fadeAnim }]}>
              <View style={styles.messageHeader}>
                <View style={[styles.avatarContainer, styles.aiAvatar]}>
                  <Brain size={14} color="#FFFFFF" />
                </View>
                <Text style={styles.messageSender}>AI Tutor</Text>
                <View style={styles.aiIndicator}>
                  <Zap size={12} color="#10B981" />
                </View>
              </View>
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Thinking...</Text>
                <View style={styles.loadingDots}>
                  <Animated.View style={[styles.dot, styles.dot1]} />
                  <Animated.View style={[styles.dot, styles.dot2]} />
                  <Animated.View style={[styles.dot, styles.dot3]} />
                </View>
              </View>
            </Animated.View>
          )}
        </ScrollView>

        {/* Quick Questions */}
        {messages.length <= 2 && (
          <View style={styles.quickQuestionsContainer}>
            <Text style={styles.quickQuestionsTitle}>Try asking:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.quickQuestionsScroll}>
              {quickQuestions.map((question, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.quickQuestionButton, { borderColor: question.color + '30' }]}
                  onPress={() => handleQuickQuestion(question.text)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.quickQuestionIcon}>{question.icon}</Text>
                  <Text style={[styles.quickQuestionText, { color: question.color }]}>{question.text}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Input Area */}
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Ask me anything about HSC Science..."
            placeholderTextColor="#9CA3AF"
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[styles.sendButton, (!inputText.trim() || isLoading) && styles.sendButtonDisabled]}
            onPress={sendMessage}
            disabled={!inputText.trim() || isLoading}
          >
            <Send size={20} color={(!inputText.trim() || isLoading) ? '#D1D5DB' : '#FFFFFF'} />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 0 : 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 22,
    fontFamily: 'Poppins-Bold',
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  messageContainer: {
    marginVertical: 8,
    padding: 20,
    borderRadius: 24,
    maxWidth: '85%',
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#667eea',
    borderBottomRightRadius: 8,
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  userAvatar: {
    backgroundColor: '#4F46E5',
  },
  aiAvatar: {
    backgroundColor: '#10B981',
  },
  messageSender: {
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    flex: 1,
  },
  aiIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageText: {
    fontSize: 15,
    lineHeight: 22,
    fontFamily: 'Inter-Regular',
  },
  userMessageText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Medium',
  },
  aiMessageContent: {
    marginBottom: 8,
  },
  messageTime: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    alignSelf: 'flex-end',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginRight: 8,
  },
  loadingDots: {
    flexDirection: 'row',
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#10B981',
    marginHorizontal: 2,
  },
  dot1: {
    opacity: 0.4,
  },
  dot2: {
    opacity: 0.6,
  },
  dot3: {
    opacity: 0.8,
  },
  quickQuestionsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  quickQuestionsTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  quickQuestionsScroll: {
    flexGrow: 0,
  },
  quickQuestionButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 16,
    marginRight: 12,
    minWidth: 140,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  quickQuestionIcon: {
    fontSize: 20,
    marginBottom: 6,
  },
  quickQuestionText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    lineHeight: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    alignItems: 'flex-end',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  textInput: {
    flex: 1,
    borderWidth: 2,
    borderColor: '#F3F4F6',
    borderRadius: 24,
    paddingHorizontal: 20,
    paddingVertical: 14,
    marginRight: 12,
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    maxHeight: 120,
    color: '#1F2937',
    backgroundColor: '#F9FAFB',
  },
  sendButton: {
    backgroundColor: '#667eea',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  sendButtonDisabled: {
    backgroundColor: '#D1D5DB',
    shadowOpacity: 0,
  },
});