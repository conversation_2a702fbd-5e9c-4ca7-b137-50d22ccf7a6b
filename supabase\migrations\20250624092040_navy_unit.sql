/*
  # Create quiz results table

  1. New Tables
    - `quiz_results`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `subject` (text)
      - `quiz_title` (text)
      - `score` (integer)
      - `total_questions` (integer)
      - `completed_at` (timestamp)

  2. Security
    - Enable <PERSON>LS on `quiz_results` table
    - Add policy for users to manage their own quiz results

  3. Indexes
    - Add index on user_id for user-specific queries
    - Add index on subject for subject-specific analytics
*/

-- Create quiz_results table
CREATE TABLE IF NOT EXISTS quiz_results (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  subject text NOT NULL,
  quiz_title text NOT NULL,
  score integer NOT NULL CHECK (score >= 0),
  total_questions integer NOT NULL CHECK (total_questions > 0),
  completed_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE quiz_results ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage own quiz results"
  ON quiz_results
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quiz_results_user_id ON quiz_results(user_id);
CREATE INDEX IF NOT EXISTS idx_quiz_results_subject ON quiz_results(user_id, subject);
CREATE INDEX IF NOT EXISTS idx_quiz_results_completed_at ON quiz_results(user_id, completed_at DESC);

-- Add constraint to ensure score doesn't exceed total questions
ALTER TABLE quiz_results ADD CONSTRAINT check_score_valid 
  CHECK (score <= total_questions);