import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, ActivityIndicator, Alert, TouchableOpacity, Text } from 'react-native';
import { WebView } from 'react-native-webview';
import { X, ExternalLink, Youtube, Maximize2 } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Linking from 'expo-linking';

interface VideoPlayerProps {
  videoId: string;
  title?: string;
  onClose?: () => void;
  showCloseButton?: boolean;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function VideoPlayer({
  videoId,
  title,
  onClose,
  showCloseButton = true
}: VideoPlayerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // YouTube embed URL with parameters for better mobile experience
  const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&showinfo=0&controls=1&modestbranding=1&playsinline=1`;

  const handleError = () => {
    setError(true);
    setLoading(false);
  };

  const handleLoad = () => {
    setLoading(false);
  };

  const openInYouTube = () => {
    const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
    Linking.openURL(youtubeUrl).catch((err) => {
      console.error('Error opening YouTube:', err);
      Alert.alert('Error', 'Could not open YouTube');
    });
  };

  if (error) {
    return (
      <View style={styles.errorContainer}>
        {showCloseButton && onClose && (
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <LinearGradient
              colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
              style={styles.closeButtonGradient}
            >
              <X size={24} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
        )}

        <View style={styles.errorContent}>
          <View style={styles.errorIcon}>
            <Youtube size={48} color="#FF0000" />
          </View>
          <Text style={styles.errorTitle}>Unable to load video</Text>
          <Text style={styles.errorText}>
            The video couldn't be loaded in the app. You can watch it on YouTube instead.
          </Text>

          <LinearGradient
            colors={['#FF0000', '#CC0000']}
            style={styles.youtubeButton}
          >
            <TouchableOpacity style={styles.youtubeButtonInner} onPress={openInYouTube}>
              <Youtube size={20} color="#FFFFFF" />
              <Text style={styles.youtubeButtonText}>Open in YouTube</Text>
            </TouchableOpacity>
          </LinearGradient>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header Controls */}
      {showCloseButton && onClose && (
        <View style={styles.headerControls}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <LinearGradient
              colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
              style={styles.closeButtonGradient}
            >
              <X size={24} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>

          {title && (
            <View style={styles.titleContainer}>
              <Text style={styles.videoTitle} numberOfLines={1}>
                {title}
              </Text>
            </View>
          )}

          <TouchableOpacity style={styles.externalButton} onPress={openInYouTube}>
            <LinearGradient
              colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)']}
              style={styles.closeButtonGradient}
            >
              <ExternalLink size={20} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}

      {loading && (
        <View style={styles.loadingContainer}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.loadingSpinner}
          >
            <ActivityIndicator size="large" color="#FFFFFF" />
          </LinearGradient>
          <Text style={styles.loadingText}>Loading video...</Text>
        </View>
      )}

      <WebView
        source={{ uri: embedUrl }}
        style={styles.webview}
        onLoad={handleLoad}
        onError={handleError}
        onHttpError={handleError}
        allowsFullscreenVideo={true}
        mediaPlaybackRequiresUserAction={false}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={false}
        scalesPageToFit={true}
        mixedContentMode="compatibility"
        originWhitelist={['*']}
        allowsInlineMediaPlayback={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  webview: {
    flex: 1,
    backgroundColor: '#000000',
  },
  headerControls: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    zIndex: 1000,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  closeButton: {
    borderRadius: 20,
  },
  closeButtonGradient: {
    borderRadius: 20,
    padding: 10,
  },
  titleContainer: {
    flex: 1,
    marginHorizontal: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  videoTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
  },
  externalButton: {
    borderRadius: 20,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    zIndex: 999,
  },
  loadingSpinner: {
    borderRadius: 40,
    padding: 20,
    marginBottom: 20,
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontFamily: 'Inter-Medium',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  errorIcon: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 40,
    padding: 20,
    marginBottom: 24,
  },
  errorTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorText: {
    color: '#CCCCCC',
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  youtubeButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  youtubeButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 12,
  },
  youtubeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
});
