/*
  # Create study progress table

  1. New Tables
    - `study_progress`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references profiles)
      - `subject` (text)
      - `chapter` (text)
      - `progress` (integer, 0-100)
      - `completed` (boolean, default false)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `study_progress` table
    - Add policy for users to manage their own progress

  3. Indexes
    - Add composite index on user_id, subject, chapter for fast lookups
    - Add index on user_id for user-specific queries
*/

-- Create study_progress table
CREATE TABLE IF NOT EXISTS study_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  subject text NOT NULL,
  chapter text NOT NULL,
  progress integer DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  completed boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, subject, chapter)
);

-- Enable RLS
ALTER TABLE study_progress ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage own study progress"
  ON study_progress
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_study_progress_user_id ON study_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_study_progress_user_subject ON study_progress(user_id, subject);
CREATE INDEX IF NOT EXISTS idx_study_progress_completed ON study_progress(user_id, completed);

-- Create trigger for updated_at
CREATE TRIGGER update_study_progress_updated_at
  BEFORE UPDATE ON study_progress
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();