/*
  # Create analytics views and functions

  1. Views
    - `user_study_analytics` - Comprehensive user study statistics
    - `subject_performance` - Subject-wise performance metrics

  2. Functions
    - `get_user_streak` - Calculate current study streak
    - `get_subject_progress` - Get detailed subject progress
*/

-- Create view for user study analytics
CREATE OR REPLACE VIEW user_study_analytics AS
SELECT 
  p.id as user_id,
  p.full_name,
  p.email,
  -- Study time statistics
  COALESCE(SUM(ss.duration), 0) as total_study_minutes,
  COALESCE(COUNT(DISTINCT DATE(ss.created_at)), 0) as study_days,
  -- Quiz statistics
  COALESCE(COUNT(qr.id), 0) as total_quizzes,
  COALESCE(AVG(CAST(qr.score AS FLOAT) / qr.total_questions * 100), 0) as average_score,
  -- Progress statistics
  COALESCE(COUNT(sp.id), 0) as total_chapters_started,
  COALESCE(COUNT(CASE WHEN sp.completed THEN 1 END), 0) as completed_chapters,
  COALESCE(AVG(sp.progress), 0) as average_progress
FROM profiles p
LEFT JOIN study_sessions ss ON p.id = ss.user_id
LEFT JOIN quiz_results qr ON p.id = qr.user_id
LEFT JOIN study_progress sp ON p.id = sp.user_id
GROUP BY p.id, p.full_name, p.email;

-- Create view for subject performance
CREATE OR REPLACE VIEW subject_performance AS
SELECT 
  sp.user_id,
  sp.subject,
  COUNT(*) as total_chapters,
  COUNT(CASE WHEN sp.completed THEN 1 END) as completed_chapters,
  AVG(sp.progress) as average_progress,
  COALESCE(AVG(CAST(qr.score AS FLOAT) / qr.total_questions * 100), 0) as average_quiz_score,
  COALESCE(SUM(ss.duration), 0) as total_study_time
FROM study_progress sp
LEFT JOIN quiz_results qr ON sp.user_id = qr.user_id AND sp.subject = qr.subject
LEFT JOIN study_sessions ss ON sp.user_id = ss.user_id AND sp.subject = ss.subject
GROUP BY sp.user_id, sp.subject;

-- Function to calculate user study streak
CREATE OR REPLACE FUNCTION get_user_streak(user_uuid uuid)
RETURNS integer AS $$
DECLARE
  streak_count integer := 0;
  current_date date := CURRENT_DATE;
  check_date date;
BEGIN
  -- Start from yesterday and count backwards
  check_date := current_date - 1;
  
  WHILE EXISTS (
    SELECT 1 FROM study_sessions 
    WHERE user_id = user_uuid 
    AND DATE(created_at) = check_date
  ) LOOP
    streak_count := streak_count + 1;
    check_date := check_date - 1;
  END LOOP;
  
  -- Check if user studied today, if so add 1
  IF EXISTS (
    SELECT 1 FROM study_sessions 
    WHERE user_id = user_uuid 
    AND DATE(created_at) = current_date
  ) THEN
    streak_count := streak_count + 1;
  END IF;
  
  RETURN streak_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get detailed subject progress
CREATE OR REPLACE FUNCTION get_subject_progress(user_uuid uuid, subject_name text)
RETURNS TABLE (
  chapter text,
  progress integer,
  completed boolean,
  last_studied timestamptz,
  quiz_attempts integer,
  best_score integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sp.chapter,
    sp.progress,
    sp.completed,
    sp.updated_at as last_studied,
    COALESCE(COUNT(qr.id), 0)::integer as quiz_attempts,
    COALESCE(MAX(CAST(qr.score AS FLOAT) / qr.total_questions * 100), 0)::integer as best_score
  FROM study_progress sp
  LEFT JOIN quiz_results qr ON sp.user_id = qr.user_id 
    AND sp.subject = qr.subject 
    AND sp.chapter = qr.quiz_title
  WHERE sp.user_id = user_uuid 
    AND sp.subject = subject_name
  GROUP BY sp.chapter, sp.progress, sp.completed, sp.updated_at
  ORDER BY sp.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;