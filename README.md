# HSC Science Hub - React Native Expo App

A comprehensive learning platform for Maharashtra HSC Science students with AI-powered tutoring, interactive quizzes, and progress tracking.

## Features

- 🤖 **AI Tutor**: Get instant help with Gemini AI integration
- 📚 **Subject Coverage**: Physics, Chemistry, Mathematics, Biology, English
- 🎯 **Practice Tests**: Interactive quizzes with detailed explanations
- 📊 **Progress Tracking**: Monitor your learning journey
- 🎥 **Video Integration**: YouTube video recommendations
- 👤 **User Profiles**: Personalized learning experience

## Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Supabase (PostgreSQL)
- **AI**: Google Gemini API
- **Video**: YouTube Data API
- **Navigation**: Expo Router
- **Authentication**: Supabase Auth

## Setup Instructions

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd hsc-science-app
npm install
```

### 2. Environment Variables

Create a `.env` file in the root directory:

```bash
cp .env.example .env
```

Fill in your API keys:

```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key
EXPO_PUBLIC_YOUTUBE_API_KEY=your_youtube_api_key
```

### 3. Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Run the database migrations in order:
   ```sql
   -- Run these in your Supabase SQL editor
   -- 1. create_profiles_table.sql
   -- 2. create_study_progress_table.sql
   -- 3. create_quiz_results_table.sql
   -- 4. create_study_sessions_table.sql
   -- 5. create_analytics_views.sql
   -- 6. seed_sample_data.sql (optional, for development)
   ```

3. Configure Authentication:
   - Go to Authentication > Settings
   - Disable "Enable email confirmations" for development
   - Configure any additional auth providers if needed

### 4. API Keys Setup

#### Google Gemini API
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file

#### YouTube Data API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create credentials (API Key)
5. Add it to your `.env` file

### 5. Run the App

```bash
npm run dev
```

## Database Schema

### Tables

1. **profiles** - User profile information
2. **study_progress** - Track learning progress by subject/chapter
3. **quiz_results** - Store quiz scores and performance
4. **study_sessions** - Log study time and activities

### Key Features

- **Row Level Security (RLS)** enabled on all tables
- **Automatic profile creation** on user signup
- **Analytics views** for performance tracking
- **Optimized indexes** for fast queries

## Project Structure

```
app/
├── (tabs)/           # Tab navigation screens
│   ├── index.tsx     # Home screen
│   ├── subjects.tsx  # Subjects overview
│   ├── ai-tutor.tsx  # AI chat interface
│   ├── practice.tsx  # Quiz interface
│   └── profile.tsx   # User profile
├── subject-detail.tsx    # Subject chapters
├── chapter-detail.tsx    # Chapter content
└── _layout.tsx      # Root layout

components/
├── AuthScreen.tsx   # Authentication UI

hooks/
├── useAuth.ts       # Authentication logic
├── useStudyProgress.ts  # Progress tracking
└── useQuizResults.ts    # Quiz management

lib/
└── supabase.ts      # Database client

supabase/
└── migrations/      # Database schema files
```

## Key Components

### Authentication
- Email/password signup and login
- Automatic profile creation
- Session management with Supabase

### AI Tutor
- Powered by Google Gemini API
- Context-aware responses for HSC Science
- Chat interface with message history

### Progress Tracking
- Subject-wise progress monitoring
- Chapter completion tracking
- Study time analytics

### Quiz System
- Interactive multiple-choice questions
- Immediate feedback and explanations
- Score tracking and analytics

## Development Notes

### Environment Setup
- Uses Expo managed workflow
- Web-first development (some native features may not work in browser)
- Requires physical device or emulator for full native features

### Database Migrations
- Run migrations in order
- Each migration includes RLS policies
- Includes sample data for development

### API Integration
- All external API calls are properly handled
- Error handling and fallbacks implemented
- Rate limiting considerations for production

## Deployment

### Database
1. Ensure all migrations are applied to production Supabase
2. Configure production environment variables
3. Set up proper RLS policies

### App Deployment
1. Configure app.json for production
2. Set up EAS Build for native apps
3. Deploy web version to hosting platform

## Contributing

1. Follow the existing code structure
2. Add proper TypeScript types
3. Include error handling
4. Test on both web and native platforms
5. Update documentation for new features

## License

This project is licensed under the MIT License.